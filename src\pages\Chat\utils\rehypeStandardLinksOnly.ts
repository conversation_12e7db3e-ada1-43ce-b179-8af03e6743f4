import { visit } from 'unist-util-visit';
import type { Element, Root } from 'hast';

/**
 * 自定义 rehype 插件，只显示标准 markdown 超链接格式
 * 标准格式：[链接文本](URL)
 * 非标准格式（如裸露的 URL）将被转换为普通文本
 */
export function rehypeStandardLinksOnly() {
  return (tree: Root) => {
    visit(tree, 'element', (node: Element, index, parent) => {
      // 只处理 a 标签
      if (node.tagName !== 'a') {
        return;
      }

      const href = node.properties?.href as string;
      if (!href) {
        return;
      }

      // 获取链接的文本内容
      const getLinkText = (children: any[]): string => {
        return children
          .map((child) => {
            if (child.type === 'text') {
              return child.value;
            }
            if (child.children) {
              return getLinkText(child.children);
            }
            return '';
          })
          .join('');
      };

      const linkText = getLinkText(node.children || []);

      // 检查是否为标准 markdown 链接格式
      // 标准格式：链接文本不等于 URL，说明是 [文本](URL) 格式
      // 非标准格式：链接文本等于 URL，说明是裸露的 URL 或 <URL> 格式
      const isStandardMarkdownLink = linkText !== href && linkText.trim() !== '';

      if (!isStandardMarkdownLink) {
        // 如果不是标准格式，将链接转换为普通文本
        if (parent && typeof index === 'number') {
          parent.children[index] = {
            type: 'text',
            value: href,
          } as any;
        }
      }
    });
  };
}
