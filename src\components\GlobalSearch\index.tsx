import { FindIcon, SearchIcon } from '@/assets/office';
import { ArrowLeftIcon } from '@/assets/svg';
import { Button, Input } from '@douyinfe/semi-ui';
import classNames from 'classnames';
import React, { useCallback, useEffect, useRef, useState } from 'react';
import styles from './index.less';

interface GlobalSearchProps {
  // 需要搜索的内容区域选择器或ref
  contentArea: string | React.RefObject<HTMLElement>;
  // 是否启用快捷键(Ctrl+F)
  enableHotkeys?: boolean;
  // 自定义类名
  className?: string;
}

const GlobalSearch: React.FC<GlobalSearchProps> = ({
  contentArea,
  enableHotkeys = true,
  className = '',
}) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [matches, setMatches] = useState<NodeListOf<HTMLElement> | null>(null);
  const [currentIndex, setCurrentIndex] = useState(-1);
  const [isVisible, setIsVisible] = useState(false);
  const searchInputRef = useRef<HTMLInputElement>(null);

  // 存储原始节点，用于恢复
  const originalNodes = useRef<Map<HTMLElement, Node | null>>(new Map());

  // 获取内容区域元素
  const getContentElement = useCallback(() => {
    if (typeof contentArea === 'string') {
      return document.querySelector<HTMLElement>(contentArea);
    }
    return contentArea.current;
  }, [contentArea]);

  // 清除所有高亮效果（正确恢复原始节点）
  const clearHighlights = useCallback(() => {
    // 恢复原始节点
    originalNodes.current.forEach((originalNode, highlightNode) => {
      const parent = highlightNode.parentNode;
      if (parent && originalNode) {
        parent.replaceChild(originalNode, highlightNode);
        parent.normalize();
      }
    });
    originalNodes.current.clear();

    setMatches(null);
    setCurrentIndex(-1);
  }, []);

  // 创建高亮元素并保存原始节点
  const createHighlightElement = useCallback((text: string, term: string) => {
    const div = document.createElement('div');
    const escapedTerm = term.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
    const regex = new RegExp(`(${escapedTerm})`, 'gi');

    div.innerHTML = text.replace(regex, `<span class="${styles.searchHighlight}">$1</span>`);

    // 收集所有高亮元素并保存原始文本节点
    const highlights = div.querySelectorAll(`.${styles.searchHighlight}`);
    highlights.forEach((highlight) => {
      const originalNode = document.createTextNode(highlight.textContent || '');
      originalNodes.current.set(highlight as HTMLElement, originalNode);
    });

    return div;
  }, []);

  // 递归处理所有文本节点
  const processTextNodes = useCallback(
    (node: Node, term: string): boolean => {
      // 跳过脚本和样式标签
      if (node.nodeType === Node.ELEMENT_NODE) {
        const element = node as HTMLElement;
        if (['SCRIPT', 'STYLE', 'NOSCRIPT'].includes(element.tagName)) {
          return false;
        }

        // 递归处理子节点（从后往前处理，避免节点列表动态变化问题）
        const childNodes = Array.from(node.childNodes).reverse();
        childNodes.forEach((child) => {
          processTextNodes(child, term);
        });
        return false;
      }

      // 处理文本节点
      if (node.nodeType === Node.TEXT_NODE) {
        const textNode = node as Text;
        const text = textNode.textContent || '';

        if (text.toLowerCase().includes(term.toLowerCase())) {
          const div = createHighlightElement(text, term);
          const parent = textNode.parentNode;

          if (parent) {
            // 移除原始文本节点
            parent.removeChild(textNode);

            // 添加处理后的节点
            Array.from(div.childNodes).forEach((child) => {
              parent.appendChild(child);
            });
          }
          return true;
        }
      }

      return false;
    },
    [createHighlightElement],
  );

  // 执行搜索
  const performSearch = useCallback(() => {
    // 清除之前的高亮
    clearHighlights();

    if (!searchTerm.trim()) {
      return;
    }

    const contentElement = getContentElement();
    if (!contentElement) return;

    // 处理所有文本节点
    processTextNodes(contentElement, searchTerm);

    // 获取所有匹配项
    const newMatches = document.querySelectorAll(`.${styles.searchHighlight}`);
    setMatches(newMatches as any);

    // 重置当前索引（不自动高亮，等待用户回车）
    setCurrentIndex(-1);
  }, [searchTerm, clearHighlights, getContentElement, processTextNodes]);

  // 更新高亮显示状态
  const updateHighlightState = useCallback(
    (newIndex: number) => {
      if (!matches || matches.length === 0) return;

      // 移除上一个选中项的高亮状态
      if (currentIndex !== -1 && currentIndex < matches.length) {
        matches[currentIndex].classList.remove(`${styles.searchHighlightCurrent}`);
      }

      // 设置新选中项的高亮状态
      if (newIndex >= 0 && newIndex < matches.length) {
        matches[newIndex].classList.add(`${styles.searchHighlightCurrent}`);
        // 滚动到视图
        matches[newIndex].scrollIntoView({
          behavior: 'smooth',
          block: 'center',
          inline: 'center',
        });
        setCurrentIndex(newIndex);
      }
    },
    [matches, currentIndex],
  );

  // 查找下一个匹配项
  const findNext = useCallback(() => {
    if (!matches || matches.length === 0) return;

    // 如果当前没有选中项（首次搜索），从第一个开始
    const nextIndex = currentIndex === -1 ? 0 : (currentIndex + 1) % matches.length;
    updateHighlightState(nextIndex);
  }, [matches, currentIndex, updateHighlightState]);

  // 查找上一个匹配项
  const findPrevious = useCallback(() => {
    if (!matches || matches.length === 0) return;

    // 如果当前没有选中项（首次搜索），从最后一个开始
    const prevIndex =
      currentIndex === -1
        ? matches.length - 1
        : (currentIndex - 1 + matches.length) % matches.length;
    updateHighlightState(prevIndex);
  }, [matches, currentIndex, updateHighlightState]);

  const handleFindClick = () => {
    setIsVisible(true);
  };

  // 监听搜索词变化
  useEffect(() => {
    if (isVisible && searchTerm) {
      // 添加防抖处理
      const debounceTimer = setTimeout(performSearch, 100);
      return () => clearTimeout(debounceTimer);
    } else if (!searchTerm) {
      clearHighlights();
    }
  }, [searchTerm, isVisible, performSearch, clearHighlights]);

  // 快捷键处理
  useEffect(() => {
    if (!enableHotkeys) return;

    const handleKeyDown = (e: KeyboardEvent) => {
      // Ctrl+F 打开/关闭搜索框
      if (e.ctrlKey && e.key.toLowerCase() === 'f') {
        e.preventDefault();
        setIsVisible(!isVisible);
      }

      // 搜索框可见时处理其他快捷键
      if (isVisible) {
        // 回车键查找下一个
        if (e.key === 'Enter') {
          e.preventDefault();
          // Shift+Enter 查找上一个
          if (e.shiftKey) {
            findPrevious();
          } else {
            findNext(); // 首次回车从第一个开始，再次回车找下一个
          }
        }

        // ESC键关闭搜索框
        if (e.key === 'Escape') {
          setIsVisible(false);
          clearHighlights();
          setSearchTerm('');
        }
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [isVisible, enableHotkeys, findNext, findPrevious, clearHighlights]);

  // 组件卸载时清除高亮
  useEffect(() => {
    return () => clearHighlights();
  }, [clearHighlights]);

  // 搜索框显示时聚焦
  useEffect(() => {
    if (isVisible && searchInputRef.current) {
      searchInputRef.current.focus();
    }
  }, [isVisible]);

  return (
    <>
      <div className={classNames(styles.globalSearchContainer, className)}>
        {isVisible ? (
          <Input
            ref={searchInputRef}
            type="text"
            value={searchTerm}
            onChange={(v) => {
              setSearchTerm(v);
            }}
            showClear={true}
            prefix={<SearchIcon />}
            suffix={
              <>
                {matches?.length ? (
                  <div className={styles.globalSearchControls}>
                    <Button
                      onClick={findPrevious}
                      className={styles.searchNavBtn}
                      theme="borderless"
                      disabled={!matches || matches.length === 0}
                      icon={<ArrowLeftIcon />}
                    />
                    <span className={classNames(styles.searchCounter)}>
                      {matches && matches.length > 0 ? (
                        currentIndex === -1 ? (
                          `${matches.length}`
                        ) : (
                          <>
                            <span className={classNames(styles.searchCounterActive)}>
                              {`${currentIndex + 1}`}
                            </span>
                            {` / ${matches.length}`}
                          </>
                        )
                      ) : (
                        ''
                      )}
                    </span>
                    <Button
                      onClick={findNext}
                      className={classNames(styles.searchNavBtn, styles.searchNavBtnRight)}
                      theme="borderless"
                      disabled={!matches || matches.length === 0}
                      icon={<ArrowLeftIcon />}
                    />
                  </div>
                ) : (
                  ''
                )}
                <Button
                  theme="borderless"
                  className={styles.globalSearchClearBtn}
                  onClick={(e) => {
                    e.stopPropagation();
                    setSearchTerm('');
                    setIsVisible(false);
                  }}
                >
                  取消
                </Button>
              </>
            }
            placeholder="输入内容回车进行搜索"
            className={styles.globalSearchInput}
            autoFocus
          />
        ) : (
          <Button
            icon={<FindIcon />}
            theme="borderless"
            className={styles.globalSearchInputFindIcon}
            onClick={handleFindClick}
          />
        )}
      </div>
    </>
  );
};

export default GlobalSearch;
