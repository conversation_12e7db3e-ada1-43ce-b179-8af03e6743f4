import Logo from '@/assets/logo.svg';
import Chat from '@/assets/office/chat.svg';
import { dispatchInUtils } from '@/utils';
import { Typography } from '@douyinfe/semi-ui';
import styles from './index.less';

const { Paragraph } = Typography;

interface QuestionItem {
  question: string;
  webSite?: string;
  routeId?: number;
}

interface ExampleProps {
  description: string;
  examplesTitle: string;
  examplesDescription: string;
  questions: QuestionItem[];
  isStoreWebSite?: boolean;
  sendMessage: (question: string, routeId?: number) => void;
}

const Example: React.FC<ExampleProps> = ({
  description,
  examplesTitle,
  examplesDescription,
  questions = [],
  isStoreWebSite = false,
  sendMessage,
}) => {
  return (
    <div className={styles.example}>
      <div className={styles.interpretTop}>
        <div className={styles.logo}>
          <img src={Logo} alt="" />
        </div>
        <div className={styles.interpretDesc}>{description}</div>
      </div>
      <div className={styles.interpretBot}>
        <div className={styles.interpretBotImg}>
          <img src={Chat} alt="" />
          <span>{examplesTitle}</span>
        </div>
        <div className={styles.interpretDescs}>{examplesDescription}</div>
        <div>
          {questions.map((v, k) => (
            <Paragraph
              key={k}
              ellipsis={{
                rows: 1,
                showTooltip: {
                  type: 'popover',
                  opts: {
                    style: {
                      width: 500,
                      backgroundColor: '#41464c',
                      borderColor: '#41464c',
                      color: 'var(--semi-color-white)',
                      borderWidth: 1,
                      borderStyle: 'solid',
                      whiteSpace: 'pre-wrap',
                      wordBreak: 'break-all',
                    },
                  },
                },
              }}
              className={styles.interpretDescItem}
              onClick={() => {
                if (isStoreWebSite) {
                  dispatchInUtils({
                    type: 'chat/updateRichTextAreaContent',
                    payload: v.webSite,
                  });
                }
                if (v.routeId) {
                  sendMessage(v.question, v.routeId);
                } else {
                  sendMessage(v.question);
                }
              }}
            >
              {v.question}
            </Paragraph>
          ))}
        </div>
      </div>
    </div>
  );
};

export default Example;
