import request from '@/utils/request';

// 获取报告生成配置信息
export const getConfigApi = '/ai/rep/getConfig';

export type configType = {
  routeId: number;
  desc: string;
  appCode: string;
  config: {
    intentions: {
      desc: string;
      url: string;
      prompt: string;
      routeId: number;
      requestType: string;
    }[];
    questions: { question: string; routeId: number }[];
  };
};

/**
 * 获取报告生成配置信息
 *
 */
export const fetchConfig = () =>
  request<configType>(getConfigApi, {
    method: 'GET',
  });
