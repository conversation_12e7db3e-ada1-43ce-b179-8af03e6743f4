import DefaultImg from '@/assets/txt2img/default-img.png';
import useImageLoader from '@/hooks/useImageLoader';
import React, { forwardRef } from 'react';
import styles from './index.less';

interface LazyImageProps {
  url: string;
  alt: string;
  height: number;
  fallbackUrl?: string;
}

const LazyImage = forwardRef<HTMLImageElement, LazyImageProps & { className?: string }>(
  ({ url, alt, height, className }, ref) => {
    const { imgRef, isLoaded, isError } = useImageLoader(url);

    React.useImperativeHandle(ref, () => imgRef.current as HTMLImageElement);

    const src = isError ? DefaultImg : url;
    return (
      <div className={`${styles.imageContainer} ${className || ''}`}>
        <img
          ref={imgRef}
          src={src}
          alt={alt}
          className={`galleryImage ${styles.galleryImage} ${
            isLoaded ? styles.loaded : styles.loading
          }`}
          loading="lazy"
          style={{
            transition: isLoaded ? 'opacity 0.1s ease-out' : 'none',
            height: `${height}px`,
          }}
        />

        {!isLoaded && !isError && <div className={styles.imagePlaceholder} />}

        {isError && (
          <div className={styles.imageError}>
            <span>加载失败</span>
          </div>
        )}
      </div>
    );
  },
);

LazyImage.displayName = 'LazyImage';
export default LazyImage;
