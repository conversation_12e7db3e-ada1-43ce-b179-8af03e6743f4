import { EditIcon } from '@/assets/chat';
import DisLike from '@/assets/chat/bad.svg';
import DisLiked from '@/assets/chat/bado.svg';
import Copy from '@/assets/chat/copy.svg';
import Delete from '@/assets/chat/del.svg';
import Like from '@/assets/chat/good.svg';
import Liked from '@/assets/chat/goodo.svg';
import Update from '@/assets/chat/update.svg';
import { AiPpt } from '@/assets/svg';
import Reference from '@/assets/svg/reference.svg';
import {
  NEED_OPEN_AI_WRITE_APPS,
  NO_COPY_BUTTON_APPS,
  NO_REFERENCE_FILE_BUTTON_APPS,
} from '@/config';
import { USER_ROLE } from '@/config/chat';
import { NEED_PASS_WEB_SITE_APPS } from '@/config/preferences';
import { dispatchInUtils, getState } from '@/utils';
import { <PERSON><PERSON>, <PERSON><PERSON>, Divider, Popconfirm, Tooltip } from '@douyinfe/semi-ui';
import type { Message } from '@douyinfe/semi-ui/lib/es/chat/interface';
import { RenderActionProps } from '@douyinfe/semi-ui/lib/es/chat/interface';
import classNames from 'classnames';
import React, { useEffect, useState } from 'react';
import { useSearchParams } from 'react-router-dom';
import { useDispatch, useParams } from 'umi';
import AiPPt from './AiPPT';
import styles from './index.less';

/**
 * 聊天内容操作区
 * @param props
 * @returns
 */
const RenderChatAction: React.FC<
  RenderActionProps & { chatRef: React.RefObject<Chat>; appCode: string; childCode: any }
> = (props) => {
  const { message, defaultActions, className, defaultActionsObj } = props;

  const dispatch = useDispatch();

  const [searchParams, setSearchParams] = useSearchParams();
  const appCode = searchParams.get('appCode') || '';
  const code = searchParams.get('code') || '';

  const { chatId = '' } = useParams<{ chatId: string }>();
  const [isReset, setIsReset] = useState(false);
  const chatMsg = getState().chat.chatMsg;
  // 如果对应的userMessage不存在，不展示ai回复底部的重新生成按钮
  useEffect(() => {
    const aiMessageId = message?.id?.split('_')[1] || '';

    const currentUserMessage = chatMsg[chatId]?.messages?.find((item: any) => {
      const id = item?.resChatDataId || item?.id;
      return item?.role === USER_ROLE && id?.split('_')[1] === aiMessageId;
    });

    setIsReset(!!currentUserMessage);
  }, [chatMsg![chatId]?.messages]);

  if (!message || !chatMsg || !chatMsg[chatId]) {
    return <div className={className}>{defaultActions}</div>;
  }

  let chatMessages = chatMsg[chatId]?.messages;
  const isUser = message.role === USER_ROLE;
  const lastUserMsgIndex = chatMessages?.findLastIndex((item: Message) => item.role === USER_ROLE);
  const lastUserMessage = lastUserMsgIndex > -1 ? chatMessages[lastUserMsgIndex] : {};

  const isLast = message.id === lastUserMessage.id;

  const isOnlyTextContent =
    // message.content?.length === 1 && (message.content[0] as Message).type === 'text';
    Array.isArray(message.content)
      ? message.content.every((item: any) => item.type !== 'image_url' && item.type !== 'file_url')
      : false;

  const checkBasicEditCondition = () => {
    return isUser && isLast && isOnlyTextContent && appCode !== 'image' && appCode !== 'ent_credit';
  };

  const checkRichTextCondition = () => {
    return !NEED_PASS_WEB_SITE_APPS.includes(Number(code) || Number(props.childCode));
  };

  const isEdit = checkBasicEditCondition() && checkRichTextCondition();
  /**
   * edit
   */
  const handleEdit = () => {
    const lastMsg = chatMessages.at(-1);
    const isUserLast = lastMsg?.role === USER_ROLE;
    // 编辑输入框显示
    message.isEdit = !message?.isEdit;
    if (isUserLast) {
      chatMessages = [...chatMessages.slice(0, -1), message];
    } else {
      chatMessages = [...chatMessages.slice(0, -2), message, lastMsg];
    }

    dispatchInUtils({
      type: 'chat/updateRichTextAreaContent',
      payload: message.webSite || '',
    });

    dispatchInUtils({
      type: 'chat/saveMessage',
      isEdit: true,
      payload: {
        appCode,
        [chatId]: {
          ...chatMsg![chatId],
          messages: chatMessages,
        },
      },
    });
  };

  let showActList = [];
  if (defaultActions && Array.isArray(defaultActions) && defaultActions?.length) {
    showActList = defaultActions.map((item: any) => item.key);
  }

  const handleCopy = () => {
    if (defaultActionsObj?.copyNode) {
      (defaultActionsObj?.copyNode as any)?.props?.onClick();
      const textContent = Array.isArray(message.content)
        ? message.content.find((item: any) => item.type === 'text')?.text || ''
        : message.content || '';

      // 使用navigator.clipboard API复制内容
      navigator.clipboard?.writeText(textContent);
    }
  };

  const handleLike = () => {
    if (defaultActionsObj?.likeNode) {
      (defaultActionsObj?.likeNode as any)?.props?.onClick();
    }
  };

  const handleDisLike = () => {
    if (defaultActionsObj?.dislikeNode) {
      (defaultActionsObj?.dislikeNode as any)?.props?.onClick();
    }
  };

  const handleReset = () => {
    if (defaultActionsObj?.resetNode) {
      if (NEED_OPEN_AI_WRITE_APPS.includes(appCode)) {
        dispatchInUtils({
          type: 'aiWrite/clearContent',
        });
        dispatchInUtils({
          type: 'chat/setRealTimeContent',
          payload: '',
        });
        dispatchInUtils({
          type: 'pageLayout/changePageMode',
          payload: appCode,
        });
        searchParams.set('pagemode', appCode);
        setSearchParams(searchParams);
      }
      (defaultActionsObj?.resetNode as any)?.props?.onClick();
    }
  };

  const onConfirm = () => {
    if (defaultActionsObj?.deleteNode) {
      (defaultActionsObj?.deleteNode as any)?.props?.onConfirm();
    }
  };

  // 获取文件内容
  const getFileContents = () => {
    if (!Array.isArray(message.content)) return [];

    return message.content.filter(
      (item: any) => item.type === 'file_url' || item.type === 'image_url',
    );
  };

  /**
   * 处理引用点击
   * @returns
   */
  const handleReferenceClick = () => {
    // 检查消息是否已发送（有id表示已发送）
    if (!isUser || !message.id) return; // 只能引用用户已发送的消息

    // 找到消息中的文件或图片
    const fileContents = getFileContents();

    if (fileContents.length > 0) {
      // 创建引用项
      const references = fileContents.map((item: any) => {
        if (item.type === 'image_url') {
          return {
            type: 'image',
            url: item.image_url?.url,
            name: item.image_url?.name || item?.name || '图片',
          };
        } else {
          // 文件类型
          return {
            type: 'file',
            url: item.file_url?.url,
            name: item.file_url?.name || item.text || '文件',
            fileTags: item.file_url?.fileTags || [],
            size: item.file_url?.size || '0KB',
            fileType: item.file_url?.extension?.toUpperCase() || 'w',
          };
        }
      });

      // 通过redux将引用发送给输入框组件
      dispatch({
        type: 'chat/addReferences',
        payload: references,
      });
    }
  };

  return (
    <>
      <div className={classNames(className, styles.chatAction)}>
        {message.status === undefined || ['complete', 'error'].includes(message.status) ? (
          <>
            {isEdit && (
              <Tooltip content={'编辑'}>
                <Button theme="borderless" onClick={handleEdit} className={styles.editBtn}>
                  <EditIcon />
                </Button>
              </Tooltip>
            )}
            {message &&
              message.id &&
              getFileContents().length > 0 &&
              !NO_REFERENCE_FILE_BUTTON_APPS.includes(appCode) && (
                <Tooltip content="引用文件">
                  <Button theme="borderless" onClick={handleReferenceClick}>
                    <img src={Reference} />
                  </Button>
                </Tooltip>
              )}
            {/* 带文件且无内容时隐藏复制 & 智慧工程-报告引用标准检测 */}
            {NO_COPY_BUTTON_APPS.includes(appCode)
              ? null
              : showActList.includes('copy') &&
                !(getFileContents().length === message.content?.length) && (
                  <Tooltip content={'复制'}>
                    <Button theme="borderless" onClick={handleCopy}>
                      <img src={Copy} />
                    </Button>
                  </Tooltip>
                )}

            {showActList.includes('reset') && isReset ? (
              <Tooltip content={'重新生成'}>
                <Button theme="borderless" onClick={handleReset}>
                  <img src={Update} />
                </Button>
              </Tooltip>
            ) : (
              ''
            )}
            <Divider layout="vertical" margin="0" />
            {showActList.includes('like') && (
              <Tooltip content={'喜欢'}>
                <Button theme="borderless" onClick={handleLike}>
                  {!message.like ? <img src={Like} /> : <img src={Liked} />}
                </Button>
              </Tooltip>
            )}
            {showActList.includes('dislike') && (
              <Tooltip content={'不喜欢'}>
                <Button theme="borderless" onClick={handleDisLike}>
                  {!message.dislike ? <img src={DisLike} /> : <img src={DisLiked} />}
                </Button>
              </Tooltip>
            )}
            {showActList.includes('delete') && (
              <Popconfirm
                title="是否删除该条消息？"
                content="删除后，消息内容无法恢复。"
                onConfirm={onConfirm}
              >
                <span className="flex">
                  <Tooltip content={'删除'}>
                    <Button theme="borderless">
                      <img src={Delete} />
                    </Button>
                  </Tooltip>
                </span>
              </Popconfirm>
            )}
          </>
        ) : null}
      </div>
      {!isUser && message.isGeneratePPT && (
        <Button
          theme="solid"
          icon={<AiPpt />}
          type="primary"
          className="!rounded-[20px] !font-normal"
          size="large"
          onClick={() => {
            dispatchInUtils({
              type: 'aiPPT/onChange',
              payload: {
                visible: true,
                resId: message.id,
                content: Array.isArray(message.content)
                  ? message.content?.find((item: any) => item.type === 'text')?.text
                  : message.content,
                chatId,
              },
            });
          }}
        >
          生成PPT
        </Button>
      )}
      {!isUser && message?.pptDetail?.userDesignId && (
        <AiPPt id={message.id} ppt={message.pptDetail} chatId={chatId} />
      )}
    </>
  );
};

export default RenderChatAction;
