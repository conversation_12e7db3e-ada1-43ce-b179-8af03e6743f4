import { CloseBtnIcon } from '@/assets/chatpdf';
import { dispatchInUtils } from '@/utils';
import { Button, Tooltip } from '@douyinfe/semi-ui';
import classNames from 'classnames';
import type { ReactNode } from 'react';
import { FC } from 'react';
import styles from './index.less';

interface Props {
  title?: string;
  onClose?: () => void;
  contentRender?: () => ReactNode;
  leftActionRender?: () => ReactNode;
  rightActionRender?: () => ReactNode;
}

const RightPane: FC<Props> = ({
  title,
  contentRender,
  leftActionRender,
  rightActionRender,
  onClose,
}) => {
  const handleClose = () => {
    dispatchInUtils({
      type: 'pageLayout/changePageMode',
      payload: '',
    });
    dispatchInUtils({
      type: 'pdfContainer/changeUrl',
      payload: {
        name: '',
      },
    });
    onClose?.();
  };

  return (
    <div className={styles.rightPane}>
      {/* 工具栏 */}
      <div className={classNames(styles.rightPaneToolsBar)}>
        {/* 左侧工具栏 */}
        <div className={styles.leftActions}>{leftActionRender?.()}</div>
        {/* 标题 */}
        {title && <div className={styles.title}>{title}</div>}
        {/* 右侧侧工具栏 */}
        <div className={styles.rightActions}>
          {rightActionRender?.()}
          <Tooltip content={'收起'}>
            <Button theme="borderless" icon={<CloseBtnIcon />} onClick={handleClose}></Button>
          </Tooltip>
        </div>
      </div>
      {/* 内容区 */}
      {contentRender?.()}
    </div>
  );
};
export default RightPane;
