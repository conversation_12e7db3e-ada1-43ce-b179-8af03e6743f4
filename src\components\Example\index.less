.example {
  margin-top: 24px;
  .interpretTop {
    display: flex;
    align-items: center;
    margin-bottom: 16px;
    .logo {
      width: 48px;
      height: 48px;
      border-radius: 50%;
      background: #eef4ff;
      margin-right: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
      img {
        width: 36px;
        height: 36px;
      }
    }
    .interpretDesc {
      // width: 368px;
      height: 44px;
      padding: 0 16px;
      font-family: PingFang SC;
      font-size: 16px;
      font-weight: 500;
      color: #000;
      background: #eef4ff;
      line-height: 44px;
      text-align: center;
      border-radius: 8px 8px 8px 0;
    }
  }
  .interpretBot {
    width: 100%;
    padding: 16px;
    border-radius: 8px;
    border: 1px solid #ebeef2;
    .interpretBotImg {
      display: flex;
      align-items: center;
      img {
        width: 24px;
        height: 24px;
        margin-right: 8px;
      }
      span {
        font-family: PingFang SC;
        font-size: 16px;
        font-weight: 500;
        color: #000;
      }
    }
    .interpretDescs {
      font-family: PingFang SC;
      font-size: 12px;
      color: #999;
      margin-top: 4px;
      margin-bottom: 16px;
    }
    .interpretDescItem {
      width: 100%;
      padding: 10px 12px;
      background: #f7f8fa;
      border-radius: 8px;
      cursor: pointer;
      margin-bottom: 12px;
    }
    .interpretDescItem:last-child {
      margin-bottom: 0;
    }
  }
}
