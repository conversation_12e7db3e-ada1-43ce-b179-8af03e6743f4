import { fetchAiPptByChatIdAndResId } from '@/services/ppt';
import { dispatchInUtils, downloadFile } from '@/utils';
import { Button, Spin, Toast } from '@douyinfe/semi-ui';
import { FC } from 'react';
import styled from './index.less';

interface PptType {
  thumbnail: string;
  title?: string;
  pdfUrl?: string;
  pptUrl?: string;
  userDesignId: number;
  pptFileSize?: string;
}

interface Props {
  id: string | undefined;
  ppt: PptType;
  chatId: string | undefined;
}

const AiPPt: FC<Props> = (props) => {
  const pptReview = () => {
    dispatchInUtils({
      type: 'pageLayout/changePageMode',
      payload: 'ppt',
    });

    console.log(11, props.ppt);

    dispatchInUtils({
      type: 'pdfContainer/changeUrl',
      payload: {
        url: props.ppt.pdfUrl,
        name: props.ppt.title,
        size: props.ppt.pptFileSize,
      },
    });
  };

  const pptDownload = () => {
    if (!props.ppt.pptUrl || !props.ppt.title) return;
    downloadFile(props.ppt.pptUrl, props.ppt.title);
  };

  const pptEdit = () => {
    fetchAiPptByChatIdAndResId({
      chatId: props.chatId,
      chatResId: props.id,
    })
      .then((res: any) => {
        const { data, code } = res;
        if (code !== 0) {
          Toast.error('获取PPT信息失败');
          return;
        }
        if (!data || data?.delete) {
          Toast.error('该PPT资源已删除');
        } else {
          dispatchInUtils({
            type: 'aiPPT/onChange',
            payload: {
              visible: true,
              resId: props.id,
              designId: props.ppt.userDesignId,
              chatId: props.chatId,
            },
          });
        }
      })
      .catch((error) => {
        Toast.error('获取PPT信息失败');
        console.error(error);
      });
  };

  return (
    <div className={styled['ai-ppt']}>
      <Spin spinning={!props.ppt.pptUrl}>
        <div className={styled['ai-ppt-preview']} onClick={pptReview}>
          <img src={props.ppt.thumbnail} />
        </div>
      </Spin>
      <div className={styled['button-group']}>
        {props.ppt.pdfUrl && (
          <Button
            type="tertiary"
            size="large"
            className="!font-normal !rounded-[20px] !outline-none"
            style={{ marginRight: 12 }}
            onClick={pptReview}
          >
            查看PPT
          </Button>
        )}
        <Button
          type="tertiary"
          size="large"
          className="!rounded-[20px] !font-normal !outline-none"
          onClick={pptEdit}
        >
          编辑PPT
        </Button>
        {props.ppt.pptUrl && (
          <Button
            type="tertiary"
            size="large"
            className="ml-3 !rounded-[20px] !font-normal !outline-none"
            onClick={pptDownload}
          >
            下载PPT
          </Button>
        )}
      </div>
    </div>
  );
};

export default AiPPt;
