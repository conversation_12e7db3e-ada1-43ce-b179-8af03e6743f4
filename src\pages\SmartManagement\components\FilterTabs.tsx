import type { MenuListItem } from '@/services/smartManagement';
import { fetchNavList } from '@/services/smartManagement';
import { Button } from '@douyinfe/semi-ui';
import React, { useEffect, useState } from 'react';
import styles from '../index.less';

interface FilterTabsProps {
  onChange?: (item: MenuListItem | undefined) => void;
}

const FilterTabs: React.FC<FilterTabsProps> = ({ onChange }) => {
  const [navList, setNavList] = useState<MenuListItem[]>([]);
  const [developingApp, setDevelopingApp] = useState<number[]>([]);
  const [activeId, setActiveId] = useState<number | undefined>(-1);

  useEffect(() => {
    fetchNavList().then((res) => {
      const list = res.data || [];
      setNavList(list);
      const developingApp = list.reduce<number[]>((acc, item) => {
        if (!item.enabled) acc.push(item.routeId);
        return acc;
      }, []);
      const isAllDeveloping = developingApp.length === list?.length;
      const firstItem = isAllDeveloping ? undefined : list?.[0];
      setActiveId(firstItem?.routeId || -1);
      setDevelopingApp(developingApp);
      onChange?.(firstItem);
    });
  }, []);

  const handleClick = (item: MenuListItem) => {
    if (developingApp.includes(item?.routeId)) return;
    setActiveId(item?.routeId);
    onChange?.(item);
  };

  return (
    <div className={styles.filterTabs}>
      {navList.map((item) => (
        <Button
          key={item.routeId}
          theme={item.routeId === activeId ? 'solid' : 'light'}
          type="tertiary"
          disabled={developingApp.includes(item.routeId)}
          className={`${styles.navButton} ${item.routeId === activeId && 'active'} ${
            developingApp.includes(item.routeId) && 'developing'
          }`}
          onClick={() => handleClick(item)}
        >
          {item.desc}
        </Button>
      ))}
    </div>
  );
};

export default FilterTabs;
