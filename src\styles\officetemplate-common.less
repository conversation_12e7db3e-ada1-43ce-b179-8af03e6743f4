// 模板组件共享样式

// 高效办公页面主样式
.officePage {
  padding: 24px 0;
  width: 800px;
  margin: 0 auto;
  position: relative;
  height: 100%;

  // 固定内容区域样式
  .fixedContentArea {
    position: fixed;
    // padding: 4px;
    top: 80px;
    width: 800px;
    bottom: 0;
  }

  .chat {
    position: fixed;
    bottom: 12px;
    width: 800px;
  }
}

// 通用容器样式
.templateContainer {
  width: 630px;
  max-width: 630px;
  padding: 12px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 10%);
}

.templateContainerMain {
  height: 450px;
  overflow-y: scroll;
}

// 导航容器样式
.navContainer {
  display: flex;
  gap: 12px;
  margin-bottom: 24px;
  // flex-wrap: wrap;

  .navButton {
    border-radius: 8px;
    padding: 6px 12px;
    border: 1px solid #EBEEF2;
    background-color: #fff;
    transition: all 0.3s ease;
    font-family: '<PERSON>Fang SC';
    font-size: 14px;
    font-weight: normal;
    height: 34px;
    width: auto;
    letter-spacing: 0;
    color: #000;

    &:hover {
      transform: translateY(-2px);
      background-color: #fff;
      border: 1px solid #EBEEF2;
    }
  }

  :global(.semi-button.active) {
    background-color: #000 !important;
    color: #fff !important;
  }

  :global(.semi-button-disabled) {
    cursor: pointer !important;
    background: #FFF !important;
    box-sizing: border-box !important;
    border: 1px solid #EBEEF2 !important;
    font-size: 14px !important;
    color: #999 !important;

    &:hover::after {
      content: '敬请期待';
      background: url('../assets/chat/coming-soon.svg') no-repeat center center;
      position: absolute;
      top: -14px;
      right: 0;
      width: 56px;
      height: 20px;
      font-size: 12px;
      line-height: 18px;
      color: #999;
    }
  }
}

// 模板网格样式
// .templateGrid {
//   display: grid;
//   grid-template-columns: repeat(4, 1fr);
//   gap: 16px;
//   max-height: 400px;
//   overflow-y: auto;
// }

// 模板项样式
// .templateItem {
//   padding: 16px;
//   border: 1px solid #ebedf0;
//   border-radius: 8px;
//   cursor: pointer;
//   transition: all 0.3s ease;

//   &:hover {
//     transform: translateY(-2px);
//     background-color: #f5f5f5;
//     box-shadow: 0 2px 8px rgba(0, 0, 0, 10%);
//   }
// }

// 图标占位符样式
.iconPlaceholder {
  width: 32px;
  height: 32px;
  border-radius: 8px;
  margin-bottom: 8px;
  display: flex;
  align-items: center;
  justify-content: center;

  img {
    width: 100%;
    height: 100%;
    object-fit: contain;
  }

  svg {
    color: #fff;
  }
}

// 模板信息样式
.templateInfo {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

// 模板标题样式
.templateTitle {
  font-size: 14px;
  font-weight: 500;
  line-height: 20px;
  color: #000;
  margin-bottom: 4px;
}

// 模板描述样式
.templateDesc {
  font-size: 12px;
  font-weight: normal;
  line-height: 18px;
  color: #999;
}

// 加载容器样式
.loadingContainer {
  width: 100%;
  padding: 30px 0;
  text-align: center;
  font-size: 14px;
  color: #999;
  grid-column: span 2;
}

// 空容器样式
.emptyContainer {
  width: 100%;
  padding: 30px 0;
  text-align: center;
  font-size: 14px;
  color: #999;
  grid-column: span 2;
}

// 模板头部样式
.templateHeader {
  // width: 800px;
  margin: 0 auto;
  border-radius: 8px 8px 0 0;
  padding: 6px 11px 8px 12px;
  background-color: #f9fafb;

  .templateHeaderContent {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .leftSection {
    display: flex;
    align-items: center;

    .editIcon {
      width: 16px;
      height: 16px;
      margin-right: 6px;
    }

    .dialogueType {
      line-height: 32px;
    }
  }

  .rightSection {
    display: flex;
    align-items: center;
  }

  :global(.templateButton) {
    background: transparent !important;

    .templateIcon {
      width: 16px;
      height: 16px;
    }

    span {
      font-size: 14px;
      color: #555;
    }
  }

  .closeButton {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 28px;
    height: 28px;
    padding: 0;

    img {
      width: 16px;
      height: 16px;
    }
  }
}

// 工具卡片样式
.toolCard {
  transition: all 0.3s ease;
  cursor: pointer;
  height: 100%;
  position: relative;

  &:hover {
    transform: translateY(-5px);
  }
}

.selectedCard {
  border: 1px solid #005bf8 !important;
}

// 返回按钮容器样式
.backToMenuContainer {
  display: flex;
  justify-content: center;
  margin-bottom: 20px;
}

// 卡片内容样式
.cardContent {
  display: flex;
  flex-direction: column;
  height: 100%;
}

// 文本内容样式
.textContent {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

// 默认图标样式
.defaultIcon {
  width: 100%;
  height: 100%;
  background-color: #e0e0e0;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
}
