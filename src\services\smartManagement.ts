/**
 * 智慧管理相关接口
 */

import request from '@/utils/request';

// 获取智慧管理菜单列表，帮我起变量名，语义化一些
export const menuListApi = '/ai/manage/getMenuList';

export interface ConfigType {
  clientId: string;
  tokenFlag?: boolean;
}

export interface MenuListItem {
  appCode: string;
  config: ConfigType;
  desc: string;
  enabled: boolean;
  routeId: number;
}

/**
 * 获取智慧管理菜单列表
 * @param data
 * @returns
 */
export const fetchNavList = () =>
  request<MenuListItem[]>(menuListApi, {
    method: 'GET',
  });
