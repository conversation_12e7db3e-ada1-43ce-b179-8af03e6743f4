import { visit } from 'unist-util-visit';

/**
 * 自定义 remark 插件，禁用自动链接功能
 * 只保留标准 markdown 链接格式 [文本](URL)
 * 将自动识别的裸露 URL 转换为普通文本
 */
export function remarkDisableAutolink() {
  return (tree: any) => {
    visit(tree, 'link', (node: any, index: any, parent: any) => {
      if (!parent || typeof index !== 'number') {
        return;
      }

      // 获取链接的文本内容
      const getLinkText = (children: any[]): string => {
        return children
          .map((child) => {
            if (child.type === 'text') {
              return child.value;
            }
            if (child.children) {
              return getLinkText(child.children);
            }
            return '';
          })
          .join('');
      };

      const linkText = getLinkText(node.children || []);
      const href = node.url;

      // 检查是否为自动链接（链接文本等于 URL）
      const isAutolink = linkText === href || linkText.trim() === '';

      if (isAutolink) {
        // 将自动链接转换为普通文本
        const textNode = {
          type: 'text',
          value: href,
        };

        parent.children[index] = textNode;
      }
    });
  };
}
