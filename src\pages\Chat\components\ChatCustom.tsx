/* eslint-disable @typescript-eslint/no-use-before-define */
import {
  <PERSON><PERSON><PERSON>,
  CloseIcon,
  CsvQuote,
  forbidden,
  OnlineActiveIcon,
  OnlineIcon,
  SendHIcon,
  SendIcon,
  StopIcon,
  TextArrow,
} from '@/assets/chat';
import enlarge from '@/assets/chat/enlarge.svg';
import file from '@/assets/chat/file.svg';
import pic from '@/assets/chat/picture.svg';
import {
  BookIcon,
  ComparisonIcon,
  CsvIcon,
  DesignIcon,
  ExcelIcon,
  PdfIcon,
  PictureIcon,
  PptIcon,
  ReferenceIcon,
  TxtIcon,
  WordIcon,
} from '@/assets/docicons';
import { AIArrowBottomSvg, Reference } from '@/assets/svg';
import {
  ALWAYS_SHOW_ONLINE_QUERY_STATUS_APPS,
  ALWAYS_SHOW_RICH_TEXT_CONTENT_APPS,
  CHAT_PLACEHOLDER,
  ENGIN_RICH_TEXT_PLACEHOLDER,
  IMAGE_SECOND_NAV_ITEMS_4_5_6,
  MENUS_APPCODES,
  NEED_OPEN_AI_WRITE_APPS,
  NO_DEFAULT_PROMPT_APPS,
  NO_ONLINE_APPS,
  NO_REFERENCE_FILE_APPS,
  NO_SHOW_LOADING_APPS,
  NO_TEMPLATE_HEADER_APPS,
  NO_UPLOAD_FILE_APPS,
  NO_UPLOAD_IMAGE_APPS,
  OFFICE_PLACEHOLDER,
  SHOW_STANDARD_AND_CALCULATE_BUTTON_APPS,
  TEXT_CREATION_IMAGE_BUTTON,
  UPLOAD_ONE_FILE_APPS,
} from '@/config';
import {
  ASSISTANT_ROLE,
  BOTTOM_TIP,
  DEFAULT_STREAM_CONTENT,
  ENGIN_FILE_CONTENT_COMPARISON_PLACEHOLDER,
  ENGIN_REPORT_REFERENCE_STANDARD_INSPECTION,
  MAX_FILE_COUNT,
  MAX_FILE_COUNT_REPORT_VALID,
  USER_ROLE,
} from '@/config/chat';
import {
  BENCHMARK_FILES_KEY,
  CALCULATION_BOOK_KEY,
  COMPARE_FILE_KEY,
  DESIGN_STANDARD_KEY,
} from '@/config/smart';
import type {
  ChatContent,
  ChatModelState,
  CustomRenderContentProps,
  ReferenceItem,
} from '@/models/chat';
import { HistoryModelState, KnowledgeItem } from '@/models/historyChat';
import type { PageLayoutModelState } from '@/models/pageLayout';
import { navItems } from '@/pages/Office/navitems';
import { deleteDataId, parseFileToMarkdown } from '@/services/chat';
import {
  addInternationalProjectPrefix,
  convertTextToRichInput,
  dispatchInUtils,
  ErrorMessageType,
  fileOnlinePreview,
  getState,
  removeInternationalProjectPrefix,
} from '@/utils';
import { IconCopy } from '@douyinfe/semi-icons';
import {
  Button,
  Chat,
  Collapsible,
  Divider,
  Image,
  MarkdownRender,
  Spin,
  Table,
  TextArea,
  Toast,
  Tooltip,
  Typography,
} from '@douyinfe/semi-ui';
import type { Message, RenderInputAreaProps } from '@douyinfe/semi-ui/lib/es/chat/interface';
import { FileItem } from '@douyinfe/semi-ui/lib/es/upload';
import classNames from 'classnames';
import React, {
  forwardRef,
  useEffect,
  useImperativeHandle,
  useLayoutEffect,
  useRef,
  useState,
} from 'react';
import rehypeMathJax from 'rehype-mathjax';
import remarkMath from 'remark-math';
import { Descendant } from 'slate';
import { useDispatch, useLocation, useParams, useSearchParams, useSelector } from 'umi';
import { ParseStatus, SmartCurrTabEnum } from '../types';
import { rehypeStandardLinksOnly } from '../utils/rehypeStandardLinksOnly';
import { remarkDisableAutolink } from '../utils/remarkDisableAutolink';
import ChatSource from './ChatSource';
import ImageContentRender from './ImageContent';
import styles from './index.less';
import KnowledgeList from './KnowledgeList';
import LoadingRender from './LoadingRender';
import TemplateHeader from './RefactoredTemplateHeader';
import RenderFileList from './RenderFileList';
import RenderRichInput, { RichInputFn } from './RenderRichInput';
import SmartUploadItem from './SmartUploadItme';
import TextCreationImageButton from './TextCreationImageButton';
import UploadComponent, { UploadFn } from './Upload';

/**
 * 富文本defaultVal
 */
const defaultVal: Descendant[] = [
  {
    type: 'paragraph',
    children: [{ text: '' }],
  },
];

// 联想查询
const OnlineQuery = ({ status = false, appCode = '' }) => {
  const dispatch = useDispatch();
  const isEdit = ALWAYS_SHOW_ONLINE_QUERY_STATUS_APPS.includes(appCode);
  const isOnlineState = getState().chat.isOnline;
  const isBothNotEmptyState = getState().chat.isBothNotEmpty;
  const OnlineQueryIcon = isBothNotEmptyState
    ? forbidden
    : isOnlineState
    ? OnlineActiveIcon
    : OnlineIcon;

  useEffect(() => {
    if (!status) {
      dispatch({
        type: 'chat/setOnlineStatus',
        payload: false,
      });
    }
  }, [status]);

  const onClick = () => {
    if (!isEdit) {
      dispatch({
        type: 'chat/setOnlineStatus',
        payload: !isOnlineState,
      });
    }
  };
  return status ? (
    <Button
      className={styles.onlineQueryBtn}
      theme="borderless"
      disabled={isBothNotEmptyState}
      type={isOnlineState ? 'primary' : 'tertiary'}
      icon={<OnlineQueryIcon />}
      onClick={onClick}
      style={{ cursor: isEdit ? 'no-drop' : 'pointer' }}
    >
      联网查询
    </Button>
  ) : null;
};

// 底部提示
export const ChatBottomSlot = () => {
  return <p className={styles.chatBottomSlot}>{BOTTOM_TIP}</p>;
};

// 猜你想问
export const CustomHintBoxRender = (props: any) => {
  const { content, onHintClick } = props;
  return (
    <div className={styles.hintBox} onClick={onHintClick} key={content}>
      {content}
      <ArrowRight />
    </div>
  );
};

// 添加通用的文件类型判断和图标渲染函数
const getFileTypeAndIcon = (fileName: string, fileTag?: string) => {
  const extension = fileName.split('.').pop()?.toLowerCase() || '';
  let fileType = 'file';
  let icon = null;
  switch (fileTag ? fileTag : extension) {
    case 'doc':
    case 'docx':
      fileType = 'word';
      icon = <WordIcon />;
      break;
    case 'xls':
    case 'xlsx':
      fileType = 'excel';
      icon = <ExcelIcon />;
      break;
    case 'ppt':
    case 'pptx':
      fileType = 'powerpoint';
      icon = <PptIcon />;
      break;
    case 'pdf':
      fileType = 'pdf';
      icon = <PdfIcon />;
      break;
    case 'txt':
      fileType = 'txt';
      icon = <TxtIcon />;
      break;
    case 'jpg':
    case 'jpeg':
    case 'png':
    case 'webp':
    case 'svg':
    case 'gif':
      fileType = 'image';
      icon = <PictureIcon />;
      break;
    case 'csv':
      fileType = 'csv';
      icon = <CsvIcon />;
      break;
    case CALCULATION_BOOK_KEY:
      fileType = extension;
      icon = <BookIcon />;
      break;
    case DESIGN_STANDARD_KEY:
      fileType = extension;
      icon = <DesignIcon />;
      break;
    case BENCHMARK_FILES_KEY:
      fileType = extension;
      icon = <ReferenceIcon />;
      break;
    case COMPARE_FILE_KEY:
      fileType = extension;
      icon = <ComparisonIcon />;
      break;
  }

  return { fileType, icon };
};

// 自定义推理内容展示
export const CustomChatContentRender = (
  props: CustomRenderContentProps & { chatRef: React.RefObject<Chat> },
) => {
  const [isOpen, setOpen] = useState(true);
  const { message, defaultContent, chatRef } = props;
  const { chatId = '' } = useParams<{ chatId: string }>();
  const [searchParams, setSearchParams] = useSearchParams();
  // const [IconChevron, setIconChevron] = useState<any>(IconChevronUp);
  const [userContent, setUserContent] = useState('');
  const dispatch = useDispatch();
  const appCode = searchParams.get('appCode') || '';
  const pageMode: string = useSelector(
    (state: { pageLayout: { mode: '' } }) => state.pageLayout.mode,
  );
  const location = useLocation();
  const pageLayout = useSelector((state: { pageLayout: PageLayoutModelState }) => state.pageLayout);

  const [isContentWrapper, setIsContentWrapper] = useState(true);
  useEffect(() => {
    setIsContentWrapper(!(pageLayout?.showtrans && pageLayout?.transhowori));
  }, [pageLayout]);
  const routeId = (location.state as any)?.routeId;
  const imageConfig = useSelector((state: any) => state.chat.imageConfig);

  // useEffect(() => {
  //   setIconChevron(isOpen ? IconChevronUp : IconChevronDown);
  //   // document.querySelectorAll('.semi-chat-chatBox-wrap').forEach((item: any) => {
  //   //   item.style.width = '100%';
  //   // });
  // }, [isOpen]);

  useEffect(() => {
    setUserContent(findTextContent());
  }, [message, message?.isEdit]);

  useEffect(() => {
    if (message?.isEdit) {
      const abortController = getState().chat.chatMsg![chatId]?.abortController;
      if (abortController) {
        abortController.abort();
        setTimeout(() => {
          dispatchInUtils({
            type: 'chat/updateAbortController',
            payload: {
              appCode,
              chatId,
              abortController: null,
              isEdit: true,
            },
          });
        }, 200);
      }
    }
  }, [message?.isEdit]);

  if (!message) {
    return <>{defaultContent}</>;
  }

  const isUser = message.role === USER_ROLE;
  const isAssistant = message.role === ASSISTANT_ROLE;
  const isError = message.status === 'error';
  const complete = message.status === 'complete' || message.status === 'incomplete';
  const isContent = message.reasoningContent && message.reasoningContent.length > 0;

  // 获取文件内容
  const getFileContents = () => {
    if (!Array.isArray(message.content)) return [];

    return message.content.filter(
      (item: any) => item.type === 'file_url' || item.type === 'image_url',
    );
  };

  // 查找文本内容
  const findTextContent = () => {
    if (!Array.isArray(message.content)) return message.content || '';

    const textItem = message.content.find((item: any) => item.type === 'text');
    return textItem?.text || '';
  };

  const buttonHandleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    setOpen(!isOpen);
    const buttonElement = event.currentTarget;
    const parentElement = buttonElement.closest('.semi-chat-chatBox-wrap') as HTMLElement;
    if (parentElement) {
      parentElement.style.width = isOpen ? '100%' : 'auto';
    } else {
      console.log('未找到具有指定类名的上层元素');
    }
  };

  const isEdit = message.isEdit;

  const ReasoningContent = () => {
    const isReasoning =
      message?.status === 'reasoning' ||
      (message?.status === 'loading' && NO_SHOW_LOADING_APPS.includes(appCode));
    return (
      <div className={styles.reasoning}>
        <Button
          theme="borderless"
          icon={
            <AIArrowBottomSvg
              className={`transition-transform duration-500 ${isOpen ? 'rotate-180' : ''}`}
            />
          }
          type="tertiary"
          iconPosition="right"
          onClick={buttonHandleClick}
        >
          {isReasoning ? (
            <div className={styles.isReasoningLoading}>
              {!NO_SHOW_LOADING_APPS.includes(appCode) && <Spin size="small" />}
              <div style={{ marginLeft: '4px' }}>深度思考中</div>
            </div>
          ) : (
            '已完成思考'
          )}
        </Button>
        <Collapsible className={styles.reasoningContent} isOpen={isOpen}>
          <pre className={styles.reasoningContentPre}>{message?.reasoningContent || ''}</pre>
        </Collapsible>
      </div>
    );
  };
  // 报告引用标准校验回复区渲染表格
  const reportValidTable = () => {
    const contentJson = message?.contentJson || {};
    if (!Object.keys(contentJson).length)
      return <div className={styles.reportValidTable}>{ErrorMessageType.reportValidFailError}</div>;
    const allTables = Object.entries(contentJson).map(([key, value]) => {
      if (!value) return null;

      const tableColumns = (value?.columns || []).map((item: any) => {
        return {
          ...item,
          align: 'center',
          render: (text: string) => {
            if (item.dataIndex === 'staStatus') {
              const tagConfig = {
                现行: { color: 'rgba(49, 188, 82, 1)' },
                作废: { color: 'rgba(255, 59, 49, 1)' },
                即将实施: { color: 'rgba(255, 199, 87, 1)' },
              } as const;
              const color = tagConfig[text as keyof typeof tagConfig]?.color;
              return <p style={{ color }}>{text}</p>;
            } else {
              return text;
            }
          },
        };
      });

      const tableData = value?.data.map((item: any, subIndex: number) => ({
        key: `${key}-${subIndex}`,
        category: value?.title || '',
        ...item,
      }));

      return (
        <div key={key}>
          <p className="title">{value?.title || ''}</p>
          <Table columns={tableColumns} dataSource={tableData} pagination={false} />
        </div>
      );
    });

    return <div className={styles.reportValidTable}>{allTables}</div>;
  };

  // 渲染用户消息
  const renderUserMessage = () => {
    const textContent = findTextContent();
    const fileContents = getFileContents();

    const handleReferenceClick = (item: any) => {
      // 创建引用项
      const reference =
        item.type === 'image_url'
          ? {
              type: 'image',
              url: item.image_url?.url,
              name: item.image_url?.name || item?.name || '图片',
            }
          : {
              type: 'file',
              url: item.file_url?.url,
              name: item.file_url?.name || item.text || '文件',
              size: item.file_url?.size || '0KB',
              fileType: item.file_url?.extension?.toUpperCase() || 'FILE',
            };

      // 通过redux添加引用
      dispatch({
        type: 'chat/updateReferences',
        payload: [reference],
      });
    };

    const handleSend = async () => {
      if (!userContent.trim()) {
        Toast.info('请输入您的问题');
        return;
      }
      const messages = getState().chat.chatMsg[chatId]?.messages;
      const lastMsg = messages.at(-1);
      const isUserLast = lastMsg?.role === USER_ROLE;
      let resultMessages = [];
      if (isUserLast) {
        resultMessages = messages.slice(0, -1);
      } else {
        resultMessages = messages.slice(0, -2);
      }

      const appCode = searchParams.get('appCode') || '';
      const params = {
        appCode,
        chatId,
      };
      let reqChatId = message?.reqChatDataId;
      let resChatId = message?.resChatDataId;
      if (!message?.reqChatDataId) {
        reqChatId = `req_${message?.id?.substring(4)}`;
        resChatId = `res_${message?.id?.substring(4)}`;
      }
      deleteDataId({
        ...params,
        dataType: 1,
        dataId: reqChatId,
      });
      deleteDataId({
        ...params,
        dataType: 2,
        dataId: resChatId,
      });
      dispatchInUtils({
        type: 'chat/saveMessage',
        payload: {
          appCode,
          [chatId]: {
            ...getState().chat.chatMsg![chatId],
            messages: resultMessages,
          },
        },
      });
      if (NEED_OPEN_AI_WRITE_APPS.includes(appCode)) {
        dispatchInUtils({
          type: 'pageLayout/changePageMode',
          payload: appCode,
        });
        dispatchInUtils({
          type: 'aiWrite/clearContent',
        });
        dispatchInUtils({
          type: 'chat/setRealTimeContent',
          payload: '',
        });
        searchParams.set('pagemode', appCode);
        setSearchParams(searchParams);
      }
      chatRef.current?.props.onMessageSend?.(userContent, []);
    };
    const handleChangeContent = (value: string) => {
      setUserContent(value);
    };
    const handleFilePreview = (fileItem: any) => {
      const isFile = fileItem.type === 'file_url';
      const fileUrl = fileItem.file_url?.url;

      let fileExt = '';
      if (fileUrl) {
        let exttmps = fileUrl.split('?')[0].split('.');
        fileExt = exttmps[exttmps.length - 1];
      }
      if (fileExt && ['pdf', 'doc', 'docx', 'ppt', 'pptx'].indexOf(fileExt) > -1) {
        dispatchInUtils({
          type: 'pageLayout/changePageMode',
          payload: 'doc',
        });

        dispatchInUtils({
          type: 'pdfContainer/changeUrl',
          payload: {
            url: fileUrl,
            name: fileItem.file_url.name,
            size: fileItem.file_url.size,
          },
        });
        return;
      }
      if (isFile) fileOnlinePreview(fileUrl, '_black');
    };

    const handleKeyDown = (event: React.KeyboardEvent<HTMLTextAreaElement>) => {
      if (event.key === 'Enter') {
        if (event.shiftKey) {
          return;
        } else {
          // 普通回车，阻止默认换行行为并发送内容
          event.preventDefault();
          if (!userContent.trim()) {
            Toast.info('请输入您的问题');
            return;
          }
          handleSend();
        }
      }
    };

    const { Title } = Typography;

    return (
      <div className={classNames('flex flex-col items-end', { [styles.bhWhite]: isEdit })}>
        {/* 文件内容 */}
        <div
          className={`relative grid gap-x-[8px] gap-y-[8px] max-w-full mb-[8px] ${styles.fileContent}`}
          style={{
            gridTemplateColumns: fileContents.find((item) => item.type === 'image_url')
              ? 'repeat(auto-fit, minmax(120px, 100px))'
              : 'repeat(auto-fit, minmax(180px, 200px))',
            display: fileContents.length ? 'grid' : 'none',
          }}
        >
          {fileContents.map((item: any, index: number) => {
            // 处理图片
            if (item.type === 'image_url') {
              const imageSrc = item.image_url?.url;
              return (
                <div
                  key={index}
                  className={styles.imageContainer}
                  style={{
                    width: '120px',
                    height: 'auto',
                    borderRadius: '4px',
                    border: '1px solid rgba(0, 0, 0, 0.12)',
                    overflow: 'hidden',
                    position: 'relative',
                  }}
                >
                  <Image
                    src={imageSrc}
                    alt={item.text || '图片'}
                    className={styles.userImage}
                    preview={true}
                  />
                  {/* <Tooltip content="引用文件">
                    <Button
                      theme="borderless"
                      className={styles.messageReferenceAction}
                      onClick={() => handleReferenceClick(item)}
                    >
                      <Reference style={{ width: '16px', height: '16px' }} />
                    </Button>
                  </Tooltip> */}
                </div>
              );
            }

            // 处理文件
            const fileName = item.file_url?.name || item.text;
            const fileTag = item.file_url?.fileTags?.[0] || '';
            const { fileType, icon } = getFileTypeAndIcon(fileName, fileTag);

            return (
              <div
                key={index}
                style={{
                  backgroundColor: '#f6f8fd',
                  borderRadius: '6px',
                  padding: '8px 12px',
                  display: 'flex',
                  gap: '4px',
                  alignItems: 'center',
                  maxWidth: '200px',
                  position: 'relative',
                }}
                onClick={() => handleFilePreview(item)}
              >
                <span
                  style={{ marginRight: '4px' }}
                  className={`${styles.referenceItemFileIcon} ${styles[`fileIcon-${fileType}`]}`}
                >
                  {icon}
                </span>
                <div style={{ flex: 1 }}>
                  <div
                    style={{
                      fontSize: '14px',
                      fontWeight: 500,
                      color: '#333',
                      whiteSpace: 'nowrap',
                      overflow: 'hidden',
                      textOverflow: 'ellipsis',
                      maxWidth: '120px',
                      marginBottom: '4px',
                    }}
                  >
                    <Title
                      ellipsis={{ showTooltip: true }}
                      style={{
                        color: '#333',
                        fontSize: '14px',
                        fontWeight: 'normal',
                        lineHeight: '22px',
                      }}
                    >
                      {fileName}
                    </Title>
                  </div>
                  <div style={{ fontSize: '12px', color: '#999' }}>
                    <span>{fileType.toUpperCase()}</span>
                    {item.file_url?.size && (
                      <>
                        <span style={{ margin: '0 4px' }}>·</span>
                        <span>{item.file_url?.size}</span>
                      </>
                    )}
                  </div>
                </div>
                {!NO_REFERENCE_FILE_APPS.includes(appCode) && (
                  <Tooltip content="引用文件">
                    <Button
                      theme="borderless"
                      className={styles.messageReferenceAction}
                      onClick={(e) => {
                        e.stopPropagation();
                        handleReferenceClick(item);
                      }}
                    >
                      <Reference style={{ width: '16px', height: '16px' }} />
                    </Button>
                  </Tooltip>
                )}
              </div>
            );
          })}
        </div>
        {/* 文本内容 */}
        {textContent &&
          (isEdit ? (
            <div className={styles.editContent}>
              <Button
                onClick={() => {
                  message.isEdit = false;
                  dispatchInUtils({
                    type: 'chat/saveMessage',
                    payload: {
                      appCode,
                      [chatId]: {
                        ...getState().chat.chatMsg![chatId],
                        messages: [...getState().chat.chatMsg![chatId]?.messages],
                      },
                    },
                  });
                }}
                icon={<CloseIcon />}
                style={{ background: '#fff', marginRight: '8px', padding: '0 8px' }}
                theme="borderless"
              ></Button>
              <TextArea
                value={userContent}
                style={{ width: '550px' }}
                autosize={{ minRows: 1, maxRows: 10 }}
                onChange={handleChangeContent}
                onKeyDown={handleKeyDown}
              ></TextArea>
              <Button
                onClick={handleSend}
                icon={<SendHIcon />}
                style={{ background: '#fff' }}
              ></Button>
            </div>
          ) : (
            <pre className={styles.defaultContent}>{textContent}</pre>
          ))}
      </div>
    );
  };

  const handleClick = (event: React.MouseEvent<HTMLDivElement>) => {
    const target = event.target as HTMLElement;
    const linkElement = target.closest('a');
    if (linkElement) {
      event.preventDefault();
      const href = linkElement.getAttribute('href');
      if (href) window.open(href, '_blank');
    }
  };

  const ZoomComponent = () => {
    const textContent = findTextContent();

    // let navigate = useNavigate();

    // const pathname = location.pathname;
    // 获取地址栏参数
    // const queryString = new URLSearchParams(searchParams).toString();

    // const chatId = pathname.split('/')[2];
    // const [searchParams] = useSearchParams();

    const handleNavigation = () => {
      const newParams = new URLSearchParams(searchParams);
      newParams.set('pagemode', appCode);
      setSearchParams(newParams);

      dispatchInUtils({
        type: 'aiWrite/setContent',
        payload: {
          content: textContent,
        },
      });

      // dispatchInUtils({
      //   type: 'chat/setRealTimeContent',
      //   payload: textContent,
      // });

      dispatchInUtils({
        type: 'pageLayout/changePageMode',
        payload: appCode,
      });

      dispatch({
        type: 'chat/setIsExpand',
        payload: true,
      });
    };

    return (
      <div onClick={handleNavigation}>
        <img src={enlarge} />
      </div>
    );

    // const queryString = window.location.search;
    // const chatId = getUrlParam(window.location.href, 'chatId');

    // // navigate(`/chat-read/${chatId}${queryString}`);
    // history.push(`/chat-read/${chatId}${queryString}`);
  };
  // 复制文本到剪贴板的函数
  const copyToClipboard = (ames: any) => {
    if (!ames) return;
    let text = '';
    if (typeof ames.content === 'string') text = ames.content;
    else if (Array.isArray(message.content)) text = ames.content[0].text;
    navigator.clipboard
      .writeText(text)
      .then(() => {
        Toast.success('复制成功');
      })
      .catch(() => {
        Toast.error('复制失败');
      });
  };
  const getMessagePreview = (message: any) => {
    if (!message?.content) return '';
    let mcontent = '';
    if (typeof message.content === 'string') {
      //mcontent = message.content.replace(/\*|#|@/g, '').slice(0, 22) + '...';
      mcontent = message.content;
    } else if (Array.isArray(message.content)) {
      mcontent = message.content[0].text;
    }

    //匹配md格式的 h1 或者 h2
    let ms = mcontent.match(/^#{1,2}\s+(.*?)\s*$/gm);
    if (ms && ms[0]) return ms[0].replace('##', '').replace('#', '').trim();
    else if (mcontent.trim().indexOf('《') === 0) {
      let _tmps = mcontent.trim().split('\n');
      return _tmps[0].trim();
    }

    return mcontent.replace(/\*|#|@/g, '').slice(0, 22) + '...';
  };

  const isReason = () => {
    const isReasoning =
      message?.status !== 'reasoning' && message?.content && message?.content?.length > 0;
    return isReasoning;
  };
  // AI写作生成中交互
  const LoadingIndicator = () => {
    const pathname = location.pathname;
    let status = message.status === 'incomplete' && pathname.includes('chat-read');
    return (
      <>
        {status && (
          <div className={styles.loadingContainer}>
            <span className={styles.text}>生成中...</span>
          </div>
        )}
      </>
    );
  };

  const onClickToChangeArt = () => {
    let content = '';
    if (message && message.content && typeof message.content === 'string') {
      dispatchInUtils({
        type: 'aiWrite/setContent',
        payload: {
          content: message.content,
        },
      });
      return;
    }

    if (
      message &&
      message.content &&
      message.content[0] &&
      (message.content[0] as ChatContent).text
    ) {
      content = (message.content[0] as ChatContent)?.text || '';
      dispatchInUtils({
        type: 'aiWrite/setContent',
        payload: {
          content,
        },
      });
    }
  };

  const isStreamError = () => {
    const isCompleted = message?.status === 'complete' || !message?.status;
    const isEmptyContent = !message?.content?.length;
    return (
      message?.role === ASSISTANT_ROLE &&
      isCompleted &&
      isEmptyContent &&
      !['report_valid', 'image'].includes(appCode) &&
      !message?.reasoningContent
    );
  };

  /**
   * 获取当前用户输入的文本（在RenderChatAction判断了如果没有对应用户消息，就不会展示重试按钮，所以这里通过id匹配没问题）
   */
  const getCurrUserMessage = () => {
    const chatMessages = getState().chat.chatMsg![chatId]?.messages || [];
    if (message?.role === ASSISTANT_ROLE) {
      const aiMessageId = message.id?.split('_')?.[1] || '';
      const currUserMessage = chatMessages.find((item: any) => {
        const id = item.resChatDataId || item.id;
        const messageId = id?.split('_')?.[1] || '';
        return item?.role === USER_ROLE && aiMessageId === messageId;
      });

      return currUserMessage || {};
    }
    return {};
  };

  const getImgHeight = () => {
    const defaultWeight = 174;
    const defaultRatio = '4:3';

    // const secondCode = searchParams.get('code');
    // if (IMAGE_SECOND_NAV_ITEMS_4_5_6.includes(secondCode || '')) {
    //   return 'auto';
    // }
    // if (TEXT_CREATION_IMAGE_BUTTON_ROUTE_ID.image_text.includes(secondCode || '')) {
    //   const [ratioW, ratioH] = defaultRatio.split(':');
    //   return Math.round((defaultWeight * Number(ratioH)) / Number(ratioW)) + 'px';
    // }

    const currentUserMessage = getCurrUserMessage();
    const ratio = currentUserMessage?.paramsJson?.ratio || defaultRatio;

    // 动态计算高度
    const ratioList = ratio.split(':') || [];
    const ratioW = Number(ratioList[0]) || 4;
    const ratioH = Number(ratioList[1]) || 3;
    return Math.round((defaultWeight * ratioH) / ratioW) + 'px';
  };

  const getAIContent = () => {
    console.log('message.content type:', typeof message?.content);
    console.log('message.content value:', message?.content);

    if (isStreamError()) {
      return <p className={styles.streamError}>{DEFAULT_STREAM_CONTENT}</p>;
    }
    if (['image'].includes(appCode)) {
      const imageStyle = {
        width: '174px',
        height: getImgHeight(),
        objectFit: 'cover' as const,
      };
      return <ImageContentRender message={message} imageStyle={imageStyle} />;
    }

    // 如果消息内容是字符串，使用自定义的 MarkdownRender
    if (typeof message?.content === 'string') {
      console.log('Using custom MarkdownRender for string content');
      return (
        <MarkdownRender
          format="md"
          raw={message.content}
          rehypePlugins={[rehypeMathJax, rehypeStandardLinksOnly]}
          remarkPlugins={[remarkMath, remarkDisableAutolink]}
          remarkGfm={true}
          components={{
            // 自定义链接组件，只渲染标准 markdown 链接
            a: ({ href, children, ...props }: any) => {
              // 获取链接文本
              const linkText =
                typeof children === 'string'
                  ? children
                  : Array.isArray(children)
                  ? children.join('')
                  : String(children || '');

              console.log('Custom link component in ChatCustom:', { href, linkText, children });

              // 如果链接文本等于 href，说明是自动链接，渲染为普通文本
              if (linkText === href || linkText.trim() === '') {
                console.log('Rendering as plain text in ChatCustom:', href);
                return <span style={{ color: 'inherit' }}>{href}</span>;
              }

              // 否则渲染为正常链接
              console.log('Rendering as link in ChatCustom:', href);
              return (
                <a href={href} {...props}>
                  {children}
                </a>
              );
            },
          }}
        />
      );
    }

    // 如果消息内容是数组，处理每个元素
    if (Array.isArray(message?.content)) {
      console.log('Using custom MarkdownRender for array content');
      return message.content.map((item: any, index: number) => {
        if (item.type === 'text') {
          return (
            <MarkdownRender
              key={index}
              format="md"
              raw={item.text}
              rehypePlugins={[rehypeMathJax, rehypeStandardLinksOnly]}
              remarkPlugins={[remarkMath, remarkDisableAutolink]}
              remarkGfm={true}
              components={{
                // 自定义链接组件，只渲染标准 markdown 链接
                a: ({ href, children, ...props }: any) => {
                  // 获取链接文本
                  const linkText =
                    typeof children === 'string'
                      ? children
                      : Array.isArray(children)
                      ? children.join('')
                      : String(children || '');

                  console.log('Custom link component in ChatCustom (array):', {
                    href,
                    linkText,
                    children,
                  });

                  // 如果链接文本等于 href，说明是自动链接，渲染为普通文本
                  if (linkText === href || linkText.trim() === '') {
                    console.log('Rendering as plain text in ChatCustom (array):', href);
                    return <span style={{ color: 'inherit' }}>{href}</span>;
                  }

                  // 否则渲染为正常链接
                  console.log('Rendering as link in ChatCustom (array):', href);
                  return (
                    <a href={href} {...props}>
                      {children}
                    </a>
                  );
                },
              }}
            />
          );
        }
        return null;
      });
    }

    console.log('Using defaultContent');
    return defaultContent;
  };

  const getImgCount = () => {
    // 还没有拿到接口数据，progressTips不存在，返回0
    const chatMessages = getState().chat.chatMsg![chatId]?.messages;
    const lastUserMsgIndex = chatMessages?.findLastIndex(
      (item: Message) => item.role === USER_ROLE,
    );
    const lastUserMessage = lastUserMsgIndex > -1 ? chatMessages[lastUserMsgIndex] : {};

    // 未开始生成，返回0
    if (appCode === 'image' && !message?.progressInfo?.name) {
      return 0;
    }
    return lastUserMessage?.paramsJson?.batchSize || 1;
  };

  return (
    <div
      className={classNames([
        styles.customChatContent,
        { [styles.customChatContentWrapper]: isContentWrapper },
      ])}
    >
      {/* AI思考内容 */}
      {isAssistant && message?.reasoningContent && ReasoningContent()}

      {isUser ? (
        renderUserMessage()
      ) : (
        <>
          {message?.status === 'loading' ? (
            <LoadingRender
              appCode={appCode}
              message={message}
              imgCount={getImgCount()}
              imgHeight={getImgHeight()}
            />
          ) : (
            ''
          )}
          <div
            className={`${isError && styles.errorContent}`}
            style={
              isAssistant && !isError && NEED_OPEN_AI_WRITE_APPS.indexOf(pageMode) < 0
                ? { backgroundColor: '#F6F8FD', borderRadius: '8px' }
                : {}
            }
            onClick={handleClick}
          >
            {NEED_OPEN_AI_WRITE_APPS.includes(appCode) ? (
              <>
                {NEED_OPEN_AI_WRITE_APPS.indexOf(pageMode) < 0 &&
                (complete ||
                  (message.role === ASSISTANT_ROLE &&
                    Array.isArray(message.content) &&
                    message.content[0]?.text)) ? (
                  <div className={styles.enlarge}>
                    <ZoomComponent />
                    <Button
                      icon={<IconCopy />}
                      theme="borderless"
                      type="tertiary"
                      size="small"
                      onClick={() => copyToClipboard(message)}
                    ></Button>
                  </div>
                ) : (
                  ''
                )}
              </>
            ) : (
              <></>
            )}

            {NEED_OPEN_AI_WRITE_APPS.indexOf(pageMode) < 0 ? (
              <>
                {appCode === 'report_valid' && message?.contentJson && reportValidTable()}
                {message?.status !== 'loading' ? getAIContent() : null}
              </>
            ) : (
              <div className={`${isContent && message.content}`}>
                {isReason() ? (
                  <div className={styles.answerContent} onClick={onClickToChangeArt}>
                    <span role="img" className={styles.answerContentIcon}>
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="1em"
                        height="1em"
                        fill="none"
                        viewBox="0 0 24 24"
                      >
                        <path
                          fill="currentColor"
                          fillRule="evenodd"
                          d="M14 3.5H5v17h14v-12h-3.5A1.5 1.5 0 0 1 14 7zm.172-2H5a2 2 0 0 0-2 2v17a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V8.328a2 2 0 0 0-.586-1.414l-4.828-4.828a2 2 0 0 0-1.414-.586M7 12.5a1 1 0 0 1 1-1h8a1 1 0 1 1 0 2H8a1 1 0 0 1-1-1m1 3a1 1 0 1 0 0 2h5a1 1 0 1 0 0-2z"
                          clipRule="evenodd"
                        ></path>
                      </svg>
                    </span>
                    <span className={styles.vditorTitle}>{getMessagePreview(message)}</span>
                  </div>
                ) : (
                  <div style={{ backgroundColor: 'rgb(246, 248, 253)', borderRadius: '8px' }}>
                    {message?.status !== 'loading' ? getAIContent() : null}
                  </div>
                )}

                <LoadingIndicator />
              </div>
            )}
            {message.quoteList && message.quoteList?.length > 0 ? (
              <ChatSource quoteList={message.quoteList} />
            ) : (
              ''
            )}
          </div>
        </>
      )}
    </div>
  );
};

// 自定义输入框
export const CustomInputRender = forwardRef<
  HTMLDivElement & { clearSmartList: () => void },
  RenderInputAreaProps & {
    placeholder?: string;
    chatTopSlot?: React.ReactNode;
    chatRef?: React.RefObject<Chat>;
    initialInputContent?: string;
    customChatFunction: Record<string, (params?: any) => void>;
    showTemplateHeader?: boolean;
    onSelectTemplate?: (template: any) => void;
    onCloseTemplate?: () => void;
    appCode: string;
    prompt: string;
    childCode: string;
    smarPlaceholder?: JSX.Element;
  }
>((props, ref) => {
  const {
    chatTopSlot,
    detailProps,
    initialInputContent,
    chatRef,
    showTemplateHeader = false,
    onSelectTemplate,
    onCloseTemplate,
    appCode = '',
    prompt = '',
    childCode = '',
  } = props || {};

  const { chatId = '' } = useParams<{ chatId: string }>();
  const { sendNode } = detailProps || {};
  const [content, setContent] = useState(initialInputContent || '');
  const dispatch = useDispatch();
  const [fileList, setFileList] = useState<any[]>([]);
  const [showDefaultPrompt, setShowDefaultPrompt] = useState(false);
  const [fileUploadDisabled, setFileUploadDisabled] = useState(false);
  const [imageUploadDisabled, setImageUploadDisabled] = useState(false);
  const [searchParams, setSearchParams] = useSearchParams();
  const secondCode = searchParams.get('code') || '';
  const appCodeFromUrl = searchParams.get('appCode') || '';
  const [selectedTitle, setSelectedTitle] = useState<KnowledgeItem | undefined>();
  // 富文本输入框引用
  const richInputRef = useRef<RichInputFn>(null);
  const templateRef = useRef<any>(null);
  const imageConfigBtnRef = useRef<any>(null);
  const [uploadKey, setUploadKey] = useState<number>(0);
  // 模版相关参数
  const [showTemplate, setShowTemplate] = useState(false);
  const [activeNav, setActiveNav] = useState('write');
  const imageRouteId = useSelector((state: any) => state.chat.imageRouteId);
  const [imageState, setImageState] = useState<boolean | null>(null);
  const imageConfig = useSelector((state: any) => state.chat.imageConfig);
  const chatState = useSelector((state: { chat: ChatModelState }) => state.chat);
  const { isExpand } = chatState;

  const onSend = (content?: string, attachment?: FileItem[]) => {
    if (chatId) {
      if (getState().chat.chatMsg![chatId]?.pending) return;
    }
    if (props?.onSend) {
      setUploadKey((prev) => prev + 1);
      props.onSend(content, attachment);
      if (NEED_OPEN_AI_WRITE_APPS.includes(appCode)) {
        dispatchInUtils({
          type: 'pageLayout/changePageMode',
          payload: appCode,
        });
        dispatchInUtils({
          type: 'aiWrite/clearContent',
        });
        dispatchInUtils({
          type: 'chat/setRealTimeContent',
          payload: '',
        });
        searchParams.set('pagemode', appCode);
        setSearchParams(searchParams);
      }
    }
  };

  useEffect(() => {
    richInputRef.current?.resetDefaultInput();
    if (selectedTitle) {
      setSelectedTitle(undefined);
    }
    templateRef.current?.toggleTemplate();
  }, [location.pathname]);

  // 从Redux中获取引用项状态，而不是本地状态
  const referenceItems = useSelector(
    (state: { chat: { references: ReferenceItem[] } }) => state.chat.references,
  );

  const [smarButtonQuote, setSmarButtonQuote] = useState(false);
  // 添加输入框引用
  const textAreaRef = useRef<HTMLTextAreaElement>(null);

  // 获取联网查询状态
  const isOnline = useSelector((state: { chat: { isOnline: boolean } }) => state.chat.isOnline);

  const smartUploadRef = useRef<any>(null);

  // 获取当前路径，用于监听路由变化
  const { pathname } = useLocation();
  const { appTypeList } = useSelector(
    (state: { historyChat: HistoryModelState }) => state.historyChat,
  );
  const isShowMenu = MENUS_APPCODES.includes(appCode);

  const APP_REQUEST_MAP = {
    knowledge: 'historyChat/fetchKnowledgeList',
    report_gen: 'historyChat/fetchConfigPage',
  };

  useEffect(() => {
    if (isShowMenu) {
      const requestType = APP_REQUEST_MAP[appCode as keyof typeof APP_REQUEST_MAP];
      if (requestType) {
        dispatch({
          type: requestType,
        });
      }
    }
  }, [appCode, isShowMenu, props.appCode]);

  function flattenArray(arr: KnowledgeItem[]): KnowledgeItem[] {
    let result: KnowledgeItem[] = [];
    for (let item of arr) {
      result.push(item);
      if (item.subTitles && item.subTitles.length > 0) {
        result = result.concat(flattenArray(item.subTitles));
      }
    }
    return result;
  }

  // 延迟等待接口刷新
  useEffect(() => {
    let isMounted = true;

    const processAppData = () => {
      if (!isMounted) return;

      if (secondCode && !selectedTitle && appTypeList.length) {
        const app: any =
          flattenArray(appTypeList).find(({ routeId }) => String(routeId) === secondCode) || {};

        if (Object.keys(app).length > 0) {
          // if (props.appCode === 'knowledge' || appCodeFromUrl === 'knowledge') {
          //   setRichInputVal(initKnowledgeValue(app.desc));
          // } else {
          //   setRichInputVal(convertTextToRichInput(app?.prompt || ''));
          // }
          app.desc = addInternationalProjectPrefix(app.desc);
          setSelectedTitle(app);
          props?.customChatFunction?.onMenuChange(app);
        }
      }
    };

    const timer = setTimeout(processAppData, 100);

    return () => {
      isMounted = false;
      clearTimeout(timer);
    };
  }, [secondCode, selectedTitle, appTypeList]);
  // useEffect(() => {
  //   if (secondCode && !selectedTitle && appTypeList.length) {
  //     const app: any =
  //       flattenArray(appTypeList).find(({ routeId }) => String(routeId) === secondCode) || {};
  //     // eslint-disable-next-line @typescript-eslint/no-use-before-define
  //     // setRichInputVal(initKnowledgeValue(app.desc));
  //     app.desc = addInternationalProjectPrefix(app.desc);
  //     setSelectedTitle(app);
  //     props?.customChatFunction?.onMenuChange(app);
  //   }
  // }, [secondCode, selectedTitle, appTypeList]);

  useEffect(() => {
    if ((chatId && !secondCode && selectedTitle) || (!chatId && selectedTitle)) {
      setSelectedTitle(undefined);
      // eslint-disable-next-line @typescript-eslint/no-use-before-define
      setRichInputVal(defaultVal);
    }
  }, [chatId, secondCode]);

  useEffect(() => {
    setShowTemplate(false);
  }, [chatId]);

  // 监听引用项变化，自动聚焦输入框
  useEffect(() => {
    if (referenceItems.length > 0 && textAreaRef.current) {
      textAreaRef.current.focus();
    }
    if (referenceItems.length > 0 && fileList.length > 0) {
      Toast.info({
        content: '已添加引用文件，上传的文件将被移除',
        duration: 3,
      });
      fileList.forEach((item) => {
        imgUpRef.current?.removeFile(item, true);
        fileUpRef.current?.removeFile(item, true);
      });
      smartUploadRef.current?.handleClearFileType1(fileList);
      setFileList([]);
      smartUploadRef.current?.handleTabClearStatus();
    }

    if (referenceItems.length > 0) {
      setSmarButtonQuote(true);
    } else {
      setSmarButtonQuote(false);
    }
  }, [referenceItems]);

  // 监听联网状态变化
  useEffect(() => {
    // 如果开启联网查询且有文件，弹出提示
    if (isOnline && fileList.length > 0) {
      Toast.info({
        content: '联网查询模式下不支持上传',
        duration: 3,
      });
    }
    setFileUploadDisabled(fileList?.[0]?.type === 'image' || isOnline);
    setImageUploadDisabled((fileList?.[0]?.type && fileList?.[0]?.type !== 'image') || isOnline);
  }, [isOnline]);

  // 监听路由变化，清除引用状态和富文本状态
  useLayoutEffect(() => {
    // 当路径变化时，清除引用状态
    dispatch({
      type: 'chat/clearReferences',
    });
    // 当不在办公页面时，清除富文本状态
    if (!pathname.includes('/office')) {
      setContent('');
      // eslint-disable-next-line @typescript-eslint/no-use-before-define
      setRichInputVal(defaultVal);
    }
  }, [pathname]);

  // 监听文件列表变化，当有文件上传时显示默认提示
  useEffect(() => {
    if (fileList.length > 0) {
      setShowDefaultPrompt(true);
    } else {
      setShowDefaultPrompt(false);
    }
    setFileUploadDisabled(fileList?.[0]?.type === 'image' || isOnline);
    setImageUploadDisabled((fileList?.[0]?.type && fileList?.[0]?.type !== 'image') || isOnline);
  }, [fileList]);

  // 当initialInputContent变化时更新content
  useEffect(() => {
    if (initialInputContent !== undefined) {
      setContent(initialInputContent);
    }
  }, [initialInputContent]);

  // 获取默认提示文字，根据上传的文件类型决定
  const getDefaultPromptText = () => {
    if (fileList.length === 0) return '';
    return fileList[0].type === 'image' ? '解释以上图片' : '以上文件的主要内容是什么';
  };

  // 获取富文本相关状态
  const { useRichText, richTextValue } = useSelector((state: any) => state.chat);
  // 富文本内容
  const [richInputVal, setRichInputVal] = useState<Descendant[]>(defaultVal);
  // 监听 Redux 中的富文本状态变化
  useEffect(() => {
    // 检查是否真的需要更新富文本状态
    const shouldUseRichText = useRichText && pathname.includes('/office');

    if (shouldUseRichText && richTextValue) {
      setRichInputVal(richTextValue);
      // 更新 content 状态
      const text = richTextValue.reduce((acc: string, node: any) => {
        if (node.children) {
          return acc + node.children.map((child: any) => child.text || '').join('');
        }
        return acc;
      }, '');
      setContent(text);
    } else if (shouldUseRichText && !richTextValue) {
      // 需要使用富文本但没有值时才重置
      setRichInputVal(defaultVal);
      setContent('');
    }
    // 如果不需要使用富文本，则不执行任何操作，保持当前状态
  }, [useRichText, richTextValue, pathname]);

  // appCode为image时，检查某些routeId下，文件列表是否为空
  const checkFileOfImageApp = () => {
    const isMustUpload =
      appCode === 'image' && IMAGE_SECOND_NAV_ITEMS_4_5_6.includes(imageRouteId || secondCode);

    return isMustUpload && fileList.length === 0;
  };

  // appCode为image时，检查富文本内容是否为空（content会拼接比例等操作内容，所以统一判断content即可）
  const checkContentOfImageApp = () => {
    const { batchSize, ratio, style } = imageConfigBtnRef.current?.getImgConfig() || {};
    const mustContentAppCode = ['4001', '4002', '4003'].includes(imageRouteId);
    const imageContent = mustContentAppCode ? `${batchSize || ''}${ratio || ''}${style || ''}` : '';
    const isEmptyContent = !content && mustContentAppCode && !imageContent;
    return appCode === 'image' && isEmptyContent;
  };

  // 发送内容前的检查工作
  const handleBeforeSend = () => {
    const isUploadPending = fileList.some((item) => item.percent < 100);
    const isParsePending = fileList.some((item) => item.parseStatus === 'pending');
    const isParseError = fileList.some((item) => item.parseStatus === 'error');

    if (checkFileOfImageApp()) {
      Toast.info('请至少上传一张原图片');
      return false;
    }

    if (checkContentOfImageApp()) {
      return false;
    }

    // 检查上传以及文件解析状态
    if (isUploadPending) {
      Toast.info('上传中，请稍等');
      return false;
    }
    if (isOnline && fileList.length) {
      Toast.info('联网查询模式下不支持上传');
      return false;
    }
    if (isParsePending) {
      Toast.info('文件解析中，请稍等');
      return false;
    }
    if (isParseError) {
      Toast.error('文件解析失败，请重新上传');
      return false;
    }
    return true;
  };

  /**
   * 发送消息主方法
   */
  const sendContent = () => {
    if (chatId) {
      if (getState().chat.chatMsg![chatId]?.pending) return;
    }
    if (!handleBeforeSend()) return;
    if (onSend) {
      // 联网查询模式下，不发送文件
      let filesToSend = isOnline ? [] : [...fileList];

      // 如果有引用的文件，添加到发送列表中
      if (!isOnline && referenceItems.length > 0) {
        // 将引用项转换为可发送的文件格式
        const referenceFiles = referenceItems.map((item) => ({
          uid: Date.now() + '_' + Math.random().toString(36).substr(2, 9),
          name: item.name || (item.type === 'image' ? '图片' : '文件'),
          type: item.type,
          url: item.url,
          status: 'done',
          fileType: item.fileType || 'word',
          fileIcon: item.fileIcon,
          size: item.size || '0KB',
          fileTags: item?.fileTags || [],
        }));

        filesToSend = [...filesToSend, ...referenceFiles];
      }

      templateRef.current?.toggleTemplate();

      onSend(content, filesToSend);
      setContent('');

      // 清理状态
      setRichInputVal(defaultVal);
      if (richInputRef.current) {
        richInputRef.current?.resetDefaultInput();
      }

      setShowDefaultPrompt(false);
      // 更新 uploadKey 以重置 Upload 组件
      setUploadKey((prev) => prev + 1);

      // 清空Redux中的引用状态
      dispatch({
        type: 'chat/clearReferences',
      });

      if (ALWAYS_SHOW_RICH_TEXT_CONTENT_APPS.includes(appCode)) {
        // 重置富文本内容
        const promptRichTextValue = convertTextToRichInput(prompt);
        setRichInputVal(promptRichTextValue);

        dispatch({
          type: 'chat/setRichTextContent',
          payload: {
            useRichText: true,
            richTextValue: promptRichTextValue,
          },
        });
      } else {
        // 清理状态
        dispatch({
          type: 'chat/setRichTextContent',
          payload: {
            useRichText: false,
            richTextValue: defaultVal,
          },
        });
      }

      smartUploadRef.current?.handleTabClearStatus();
      smartUploadRef.current?.handleClearFileType1(fileList);
      fileList.forEach((item) => {
        imgUpRef.current?.removeFile(item, true);
        fileUpRef.current?.removeFile(item, true);
      });
      setFileList([]);
    }
  };

  // 点击默认提示时的处理函数
  const handleDefaultPromptClick = () => {
    if (chatId) {
      if (getState().chat.chatMsg![chatId]?.pending) return;
    }
    if (!handleBeforeSend()) return;
    const promptText = getDefaultPromptText();
    setContent(promptText);
    // 直接调用发送函数，不使用setTimeout
    if (onSend) {
      // 联网查询模式下，不发送文件
      let filesToSend = isOnline ? [] : [...fileList];
      onSend(promptText, filesToSend);

      smartUploadRef.current?.handleTabClearStatus();
      smartUploadRef.current?.handleClearFileType1(fileList);
      fileList.forEach((item) => {
        imgUpRef.current?.removeFile(item, true);
        fileUpRef.current?.removeFile(item, true);
      });
      // 清理状态
      setContent('');
      setFileList([]);
      setShowDefaultPrompt(false);

      // 清空Redux中的引用状态
      dispatch({
        type: 'chat/clearReferences',
      });
    }
  };
  const handleImgConfigChange = (config: any) => {
    setImageState(config);
  };
  const isDisabled =
    ((!content.trim() && fileList.length === 0) || getState().chat.chatMsg![chatId]?.pending) &&
    referenceItems.length === 0;

  const isStopGenerate = getState().chat.chatMsg![chatId]?.pending;
  const isImage = appCode === 'image' ? imageState : !isDisabled;
  const SendIconByStatus = isImage ? SendHIcon : SendIcon;

  const onSubmit = () => {
    // 如果有生成中的内容，停止生成；否则发送消息
    if (isStopGenerate) {
      chatRef?.current?.props.onStopGenerator?.();
    } else {
      if (!referenceItems.length) {
        if (smartUploadRef.current?.handleSubmitSmart()) {
          return;
        }
      }

      sendContent();
    }
  };

  // 创建一个包装了原生sendNode的自定义发送按钮
  const customSendButton = sendNode ? (
    <Tooltip content={isStopGenerate ? '停止生成' : '发送消息'}>
      <Button
        icon={isStopGenerate ? <StopIcon /> : <SendIconByStatus />}
        style={{ padding: '0' }}
        className="!p-0 !w-[24px] !h-[24px]"
        theme="borderless"
        onClick={onSubmit}
        aria-label="发送"
        disabled={
          appCode === 'image' ? !imageState && !isStopGenerate : !isStopGenerate && isDisabled
        }
      />
    </Tooltip>
  ) : null;

  const uploadProgress = (percent: number, file: File, fileTag?: string) => {
    const { fileType, icon } = getFileTypeAndIcon(file.name, fileTag);
    const formatFileSize = (size: number) => {
      if (size < 1024) {
        return size + 'B';
      } else if (size < 1024 * 1024) {
        return (size / 1024).toFixed(1) + 'KB';
      } else {
        return (size / (1024 * 1024)).toFixed(1) + 'MB';
      }
    };
    const currentFile = {
      ...file,
      name: file.name,
      type: fileType,
      rawSize: file.size,
      size: formatFileSize(file.size),
      fileType: fileType,
      fileIcon: icon,
      percent,
      fileTags: fileTag ? [fileTag] : [],
    };
    setFileList((prevFileList) => {
      const existingFileIndex = prevFileList.findIndex(
        (item) => item.uid === (file as File & { uid: string }).uid,
      );
      if (existingFileIndex !== -1) {
        return prevFileList.map((item, index) =>
          index === existingFileIndex ? { ...item, percent } : item,
        );
      }
      return [...prevFileList, currentFile];
    });
  };

  const onSuccess = async (resp: any, file: File, uploadType: string) => {
    // 如果有引用内容，清空引用内容
    if (referenceItems.length > 0) {
      // 清空Redux中的引用状态
      dispatch({
        type: 'chat/clearReferences',
      });
    }

    if (uploadType === 'file') {
      let transferUrl = '';
      let parseStatus: ParseStatus = 'pending';
      // 统一更新文件列表
      setFileList((prevFileList) => {
        return prevFileList.map((item) =>
          item.uid === (file as File & { uid: string }).uid
            ? { ...item, url: resp.data, type: uploadType, transferUrl, parseStatus }
            : item,
        );
      });
      try {
        const url = typeof resp.data === 'string' ? resp.data : resp.data.url;
        const res = await parseFileToMarkdown(url);
        if (url) {
          if (SHOW_STANDARD_AND_CALCULATE_BUTTON_APPS.includes(appCode) && res.msg) {
            Toast.info({
              content: res.msg,
              duration: 3,
            });
          }
          transferUrl = res?.data?.url;
          parseStatus = 'success';
        } else {
          parseStatus = 'error';
        }
      } catch (error) {
        parseStatus = 'error';
      } finally {
        setFileList((prevFileList) => {
          return prevFileList.map((item) =>
            item.uid === (file as File & { uid: string }).uid
              ? { ...item, url: resp.data, type: uploadType, transferUrl, parseStatus }
              : item,
          );
        });
      }
    } else {
      setFileList((prevFileList) => {
        return prevFileList.map((item) =>
          item.uid === (file as File & { uid: string }).uid
            ? {
                ...item,
                url: resp.data,
                type: uploadType,
                transferUrl: '',
                parseStatus: 'success',
              }
            : item,
        );
      });
    }
  };

  const onKeyDown = (event: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (event.key === 'Enter' && !event.shiftKey) {
      event.preventDefault(); // 阻止默认的换行行为
      if (chatId) {
        if (getState().chat.chatMsg![chatId]?.pending) return;
      }
      if (!referenceItems.length) {
        if (smartUploadRef.current?.handleSubmitSmart()) {
          return;
        }
      }
      sendContent();
      setContent('');
    }
  };

  const fileUpRef = useRef<UploadFn>(null);
  const imgUpRef = useRef<UploadFn>(null);
  /**
   * 删除文件
   * @param file
   */
  const handleRemoveFile = (file: any) => {
    if (!file) return;

    const uid = file.uid;
    if (!uid) return;

    const newFileList = fileList.filter((item) => item.uid !== uid);

    setFileList(newFileList);
    if (fileUploadDisabled) {
      imgUpRef.current?.removeFile(file);
    } else {
      fileUpRef.current?.removeFile(file);
    }

    smartUploadRef.current?.handleClearFileType(file, uid);
  };

  const clearSmartList = () => {
    smartUploadRef.current?.handleClearFileType1(fileList);
    smartUploadRef.current?.handleTabClearStatus();
    setFileList([]);
    setContent('');
  };

  useImperativeHandle(ref, () => ({
    ...(ref as any),
    clearSmartList,
    getImgConfig: () => imageConfigBtnRef.current?.getImgConfig(),
    imgConfigBtnReset: () => imageConfigBtnRef.current?.getReset(),
  }));
  // 智慧工程在切换tab时将上传的文件清理
  // useEffect(() => {
  //   if (SHOW_STANDARD_AND_CALCULATE_BUTTON_APPS.includes(appCode)) {
  //     smartUploadRef.current?.handleClearFileType1(fileList);
  //     smartUploadRef.current?.handleTabClearStatus();
  //     setFileList([]);
  //     setContent('');
  //   }
  // }, [appCode]);

  // 移除引用项 - 更新为使用Redux
  const handleRemoveReference = (index: number) => {
    fileList.forEach((item) => {
      imgUpRef.current?.removeFile(item, true);
      fileUpRef.current?.removeFile(item, true);
    });
    // -1表示清空所有引用
    if (index === -1) {
      dispatch({
        type: 'chat/updateReferences',
        payload: [],
      });
      return;
    }
    const newReferenceItems = [...referenceItems];
    newReferenceItems.splice(index, 1);

    // 更新Redux中的引用状态
    dispatch({
      type: 'chat/updateReferences',
      payload: newReferenceItems,
    });
  };

  const initKnowledgeValue = (title: string): Descendant[] => [
    {
      type: 'paragraph',
      children: [
        {
          text: '基于',
        },
        {
          type: 'labeled_input',
          placeholder: '',
          key: 'name',
          editable: true,
          children: [{ text: title }],
        },
        {
          type: 'labeled_input',
          placeholder: '输入您的问题',
          key: 'qs',
          editable: true,
          children: [{ text: '' }],
        },
        {
          text: '',
        },
      ],
    },
  ];

  // 文生图tab切换清空上传图片
  // const routeId = searchParams.get('routeId') || '';
  useEffect(() => {
    setFileList([]);
  }, [imageRouteId]);

  const placeholder = chatRef?.current?.props.placeholder;

  const setKnowledgeCode = (app?: KnowledgeItem) => {
    if (!chatId) return;
    const newParams = new URLSearchParams(searchParams);
    if (app?.routeId) {
      newParams.set('code', app.routeId);
    } else {
      newParams.delete('code');
      if (!isExpand) {
        newParams.delete('pagemode');
      }
    }
    setSearchParams(newParams);
  };

  const { Title } = Typography;

  const referenceItem = referenceItems?.slice(0, 1);

  const handleUpdateButton = () => {
    // 提取 accept 和 content 的生成逻辑
    const getAcceptAndContent = () => {
      const isStandardTesting =
        OFFICE_PLACEHOLDER[appCode as keyof typeof OFFICE_PLACEHOLDER]?.fileFormat.length > 0 ||
        (SHOW_STANDARD_AND_CALCULATE_BUTTON_APPS.includes(appCode) &&
          SHOW_STANDARD_AND_CALCULATE_BUTTON_APPS[SmartCurrTabEnum.report_valid - 1] === appCode);

      return isStandardTesting
        ? { accept: '.pdf,.txt,.docx,.doc', content: 'pdf、txt、docx、doc' }
        : {
            accept: '.pdf,.txt,.docx,.doc,.xlsx,.xls,.pptx,.ppt',
            content: 'pdf、txt、docx、doc、xlsx、xls、pptx、ppt等格式',
          };
    };

    const { accept, content } = getAcceptAndContent();

    const uploadLimit = UPLOAD_ONE_FILE_APPS.includes(appCode)
      ? MAX_FILE_COUNT_REPORT_VALID
      : MAX_FILE_COUNT;

    const upload = (
      <UploadComponent
        key={`file-upload-${uploadKey}`}
        ref={fileUpRef}
        accept={accept}
        isIcon={true}
        onExceed={() => Toast.info(`最多只能上传${uploadLimit}个文件`)}
        disabled={fileUploadDisabled}
        // 自定义属性参数用来判断是否只需要上传1一个文件
        isUploadOne={
          UPLOAD_ONE_FILE_APPS.includes(appCode) && fileList.length === MAX_FILE_COUNT_REPORT_VALID
        }
        limit={fileUploadDisabled ? 0 : uploadLimit}
        multiple={UPLOAD_ONE_FILE_APPS.includes(appCode) ? false : true}
        onProgress={(percent, file) => uploadProgress(percent, file)}
        onSuccess={(resp, file) => onSuccess(resp, file, 'file')}
        UploadButton={
          <div>
            <Tooltip
              content={
                isOnline
                  ? '联网查询模式下不支持上传文件'
                  : fileUploadDisabled
                  ? '已上传图片，不能同时上传文件'
                  : `最多上传${uploadLimit}个文件，支持${
                      content.includes('格式') ? '' : '格式'
                    }：${content}，单个文件大小不超过10MB`
              }
            >
              <Button
                className="!p-0 !w-[24px] !h-[24px]"
                theme="borderless"
                icon={<img src={file} />}
                aria-label="上传文件"
                disabled={fileUploadDisabled}
                onClick={(e) => e.currentTarget.blur()}
              />
            </Tooltip>
          </div>
        }
      />
    );

    // 简化返回逻辑
    if (!NO_UPLOAD_FILE_APPS.includes(appCode)) {
      return upload;
    } else if (
      SHOW_STANDARD_AND_CALCULATE_BUTTON_APPS.includes(appCode) &&
      SHOW_STANDARD_AND_CALCULATE_BUTTON_APPS[SmartCurrTabEnum.report_valid - 1] === appCode
    ) {
      return upload;
    }

    return null;
  };

  // 输入框下部的按钮位置
  const isButtonEnd = () => {
    if (
      SHOW_STANDARD_AND_CALCULATE_BUTTON_APPS.includes(appCode) &&
      SHOW_STANDARD_AND_CALCULATE_BUTTON_APPS[SmartCurrTabEnum.report_valid - 1] !== appCode
    ) {
      return false;
    } else if (NO_DEFAULT_PROMPT_APPS.includes(appCode)) {
      return true;
    }
    return '';
  };

  useEffect(() => {
    const status = fileList.length > 0 || referenceItems.length > 0;
    dispatch({
      type: 'chat/setBothNotEmpty',
      payload: status,
    });
  }, [referenceItems, fileList]);

  // 判断是否显示分割线
  const shouldShowFileUpload = !NO_UPLOAD_FILE_APPS.includes(appCode);
  const shouldShowImageUpload = !NO_UPLOAD_IMAGE_APPS.includes(appCode);
  const smarShowLine =
    SHOW_STANDARD_AND_CALCULATE_BUTTON_APPS.includes(appCode) &&
    SHOW_STANDARD_AND_CALCULATE_BUTTON_APPS[SmartCurrTabEnum.report_valid - 1] === appCode;
  const shouldShowButtons = shouldShowFileUpload || shouldShowImageUpload || smarShowLine;

  const appCodePlaceholders: { [key: string]: string } = {
    [SHOW_STANDARD_AND_CALCULATE_BUTTON_APPS[SmartCurrTabEnum.engin - 1]]:
      ENGIN_RICH_TEXT_PLACEHOLDER,
    [SHOW_STANDARD_AND_CALCULATE_BUTTON_APPS[SmartCurrTabEnum.report_compare - 1]]:
      ENGIN_FILE_CONTENT_COMPARISON_PLACEHOLDER,
    [SHOW_STANDARD_AND_CALCULATE_BUTTON_APPS[SmartCurrTabEnum.report_valid - 1]]:
      ENGIN_REPORT_REFERENCE_STANDARD_INSPECTION,
  };

  const getAppPlaceholder = () => {
    const navListOfImageApp = getState().text2Img.navList || [];
    const routeId = imageRouteId || secondCode;
    const currNavInfo = navListOfImageApp.find((item: any) => `${item?.routeId}` === routeId);

    if (appCode === 'image' && IMAGE_SECOND_NAV_ITEMS_4_5_6.includes(routeId)) {
      return currNavInfo?.prompt || '';
    }
    const appPlaceholder = OFFICE_PLACEHOLDER[appCode as keyof typeof OFFICE_PLACEHOLDER];
    return appPlaceholder?.placeholder ?? placeholder ?? CHAT_PLACEHOLDER;
  };

  const handleTxtArea = () => {
    if (SHOW_STANDARD_AND_CALCULATE_BUTTON_APPS.includes(appCode)) {
      return (
        <div className={styles.chatInputTextAreawrapper}>
          <TextArea
            borderless
            value={content}
            onChange={setContent}
            className={styles.chatInputTextArea}
            autosize={{ minRows: 2, maxRows: 10 }}
            placeholder={appCodePlaceholders[appCode]}
            onKeyDown={onKeyDown}
            ref={textAreaRef}
          ></TextArea>
          {/* {!content && (
            <div className={styles.chatInputTextAreaPlaceholder}>
              {smarPlaceholder || smarInputPlaceholder}
            </div>
          )} */}
        </div>
      );
    } else {
      return (
        <TextArea
          borderless
          value={content}
          onChange={setContent}
          className={styles.chatInputTextArea}
          placeholder={getAppPlaceholder()}
          autosize={{ minRows: 2, maxRows: 10 }}
          onKeyDown={onKeyDown}
          ref={textAreaRef}
        ></TextArea>
      );
    }
  };

  // 富文本输入更新
  const handleRichInputChange = (value: string) => {
    const richTextValue = convertTextToRichInput(value);
    setContent(value);
    setRichInputVal(richTextValue);
  };

  const getTemplateHeadTitle = () => {
    const navItem = navItems.filter((item) => item.key === appCode);
    return navItem?.[0]?.text || '';
  };

  return (
    <>
      {chatTopSlot}
      {isShowMenu && !selectedTitle?.desc && !secondCode && (
        <KnowledgeList
          onChange={(app) => {
            if (props.appCode === 'knowledge' || appCodeFromUrl === 'knowledge') {
              setRichInputVal(initKnowledgeValue(app.desc));
            } else {
              setRichInputVal(convertTextToRichInput(app?.prompt || ''));
            }
            app.desc = addInternationalProjectPrefix(app.desc);
            setSelectedTitle(app);
            setKnowledgeCode(app);
            props?.customChatFunction?.onMenuChange(app);
          }}
        />
      )}
      {/* {isShowMenu && selectedTitle?.desc && <div className="h-[10px]"></div>} */}
      <div className={styles.chatInput}>
        <div className={styles.chatInputContent}>
          {isShowMenu && (secondCode || selectedTitle?.desc) && (
            <div className={styles.chatInputTitle}>
              {selectedTitle?.desc && (
                <>
                  <span className={'flex flex-row items-center'}>
                    <img src={selectedTitle?.url || ''} className="mr-[4px]" />
                    {selectedTitle?.desc || ''}
                  </span>
                  <span
                    className={styles.chatInputClose}
                    onClick={() => {
                      if (selectedTitle) {
                        selectedTitle.desc = removeInternationalProjectPrefix(selectedTitle.desc);
                      }
                      setSelectedTitle(undefined);
                      setKnowledgeCode();
                      setRichInputVal(defaultVal);
                      richInputRef?.current?.resetDefaultInput();
                      props?.customChatFunction?.onMenuChange('');
                    }}
                  >
                    ×
                  </span>
                </>
              )}
            </div>
          )}
          {showTemplateHeader && appCodeFromUrl && !NO_TEMPLATE_HEADER_APPS.includes(appCode) && (
            <TemplateHeader
              ref={templateRef}
              currNav={activeNav}
              changeNav={setActiveNav}
              title={getTemplateHeadTitle()}
              onSelectTemplate={onSelectTemplate}
              onClose={onCloseTemplate}
              showTemplate={showTemplate}
              setShowTemplate={(v) => {
                setActiveNav(appCode);
                setShowTemplate(v);
              }}
            />
          )}
          <div className={styles.chatInputUploadFile}>
            <div className={styles.uploadFileList}>
              {!!fileList?.length && (
                <RenderFileList
                  fileList={fileList}
                  childCode={childCode}
                  updateRatio={(e) => imageConfigBtnRef.current.updateRatio(e)}
                  handleRemoveFile={handleRemoveFile}
                  isImageUploadDisabled={imageUploadDisabled}
                />
              )}
            </div>
          </div>
          {/* 默认提示 */}
          {!NO_DEFAULT_PROMPT_APPS.includes(appCode) &&
            showDefaultPrompt &&
            !TEXT_CREATION_IMAGE_BUTTON.includes(appCode) && (
              <div className={styles.defaultPromptContainer}>
                <span
                  onClick={handleDefaultPromptClick}
                  className={styles.defaultPrompt}
                  style={{ display: 'flex', alignItems: 'center' }}
                >
                  {getDefaultPromptText()}
                  <TextArrow style={{ marginLeft: '4px' }} />
                </span>
              </div>
            )}
          {/* 显示引用项部分 - 只显示文件信息 */}
          {referenceItems.length > 0 && (
            <div className={styles.chatInputReference}>
              <Reference />
              <p>
                {referenceItem.map((item, index) => {
                  if (item.type === 'image') {
                    return (
                      <span key={index} className={styles.referenceItem}>
                        <span className={styles.referenceItemContent}>
                          <span
                            className={`${styles.referenceItemFileIcon} ${styles['fileIcon-image']}`}
                          >
                            <PictureIcon />
                          </span>
                          <div className="flex items-center text-sm">
                            <Title
                              ellipsis={{ showTooltip: true }}
                              style={{
                                fontSize: '14px',
                                fontWeight: 'normal',
                                lineHeight: '22px',
                                color: '#555555',
                                maxWidth: '500px',
                              }}
                            >
                              {referenceItem?.[0]?.name || '图片'}
                            </Title>
                            {referenceItems.length > 1 &&
                              `和其他${referenceItems.slice(1).length}个图片`}
                          </div>
                        </span>
                        <span
                          className={styles.referenceDelete}
                          onClick={() => handleRemoveReference(-1)}
                        >
                          <CloseIcon />
                        </span>
                      </span>
                    );
                  }

                  const fileTag = item?.fileTags?.[0] || '';
                  const { fileType, icon } = getFileTypeAndIcon(item.name || '', fileTag);

                  return (
                    <span key={index} className={styles.referenceItem}>
                      <span className={styles.referenceItemContent}>
                        <span
                          className={classNames(styles.referenceItemFileIcon, {
                            'fileIcon-smart': fileTag,
                          })}
                        >
                          {fileType === 'csv' ? <CsvQuote /> : icon}
                        </span>
                        <div className="flex items-baseline text-sm">
                          <Title
                            ellipsis={{ showTooltip: true }}
                            style={{
                              fontSize: '14px',
                              fontWeight: 'normal',
                              lineHeight: '22px',
                              color: '#555555',
                              maxWidth: '500px',
                            }}
                          >
                            {item.name || '文件'}
                          </Title>
                          {referenceItems.length > 1 &&
                            `和其他${referenceItems.slice(1).length}个文件`}
                        </div>
                      </span>
                      <span
                        className={styles.referenceDelete}
                        onClick={() => handleRemoveReference(-1)}
                      >
                        <CloseIcon />
                      </span>
                    </span>
                  );
                })}
              </p>
            </div>
          )}
          {/* 使用富文本或普通文本区域 */}
          {useRichText && richTextValue ? (
            <div className={styles.chatDivInputTextArea}>
              <div className="max-h-[300px] overflow-auto min-h-[40px]">
                <RenderRichInput
                  ref={richInputRef}
                  appCode={appCode}
                  childCode={childCode}
                  initialValue={richTextValue}
                  onChange={handleRichInputChange}
                  beforeSend={handleBeforeSend}
                  onSend={sendContent}
                  placeholder={getAppPlaceholder()}
                />
              </div>
            </div>
          ) : !isShowMenu ? (
            handleTxtArea()
          ) : (
            <div className={styles.chatDivInputTextArea}>
              <div className="max-h-[300px] overflow-auto min-h-[40px]">
                <RenderRichInput
                  ref={richInputRef}
                  appCode={appCode}
                  beforeSend={handleBeforeSend}
                  initialValue={richInputVal}
                  onChange={setContent}
                  onSend={sendContent}
                  placeholder={getAppPlaceholder()}
                  childCode={childCode}
                />
              </div>
            </div>
          )}
          <div
            className={classNames(styles.chatInputAction, {
              '!justify-end': isButtonEnd(),
            })}
          >
            <SmartUploadItem
              appCode={appCode || appCodeFromUrl}
              onSuccess={onSuccess}
              uploadKey={uploadKey}
              ref={smartUploadRef}
              uploadProgress={uploadProgress}
              beforeRichInputSend={handleBeforeSend}
              smarButtonQuote={smarButtonQuote}
            />

            <div>
              <OnlineQuery
                status={!NO_ONLINE_APPS.includes(appCode)}
                appCode={appCode || appCodeFromUrl}
              />
              {TEXT_CREATION_IMAGE_BUTTON.includes(appCode) && (
                <TextCreationImageButton
                  ref={imageConfigBtnRef}
                  content={content}
                  onSuccess={onSuccess}
                  uploadProgress={uploadProgress}
                  fileList={fileList}
                  setFileList={setFileList}
                  chatId={chatId}
                  onImgConfigChange={handleImgConfigChange}
                  appCode={appCode}
                />
              )}
            </div>
            <div className="flex gap-[8px] items-center">
              {handleUpdateButton()}
              {!NO_UPLOAD_IMAGE_APPS.includes(appCode) && (
                <UploadComponent
                  key={`image-upload-${uploadKey}`}
                  ref={imgUpRef}
                  onExceed={() => {
                    Toast.info(`最多只能上传${MAX_FILE_COUNT}张图片`);
                  }}
                  onSuccess={(resp, file) => onSuccess(resp, file, 'image')}
                  onProgress={(percent, file) => uploadProgress(percent, file)}
                  accept="image/*"
                  isIcon={true}
                  disabled={imageUploadDisabled}
                  limit={imageUploadDisabled ? 0 : MAX_FILE_COUNT}
                  multiple={true}
                  UploadButton={
                    <div>
                      <Tooltip
                        content={
                          isOnline
                            ? '联网查询模式下不支持上传图片'
                            : imageUploadDisabled
                            ? '已上传文件，不能同时上传图片'
                            : `最多${MAX_FILE_COUNT}张，单张最大10M，支持jpg、jpeg、png等`
                        }
                      >
                        <Button
                          className="!p-0 !w-[24px] !h-[24px]"
                          theme="borderless"
                          icon={<img src={pic} />}
                          aria-label="上传图片"
                          disabled={imageUploadDisabled}
                          onClick={(e) => e.currentTarget.blur()}
                        />
                      </Tooltip>
                    </div>
                  }
                />
              )}
              {shouldShowButtons && <Divider layout="vertical" className="!mx-[4px]" />}
              <div className={styles.uploadButton}>{customSendButton}</div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
});
