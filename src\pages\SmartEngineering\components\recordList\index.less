.recordList {
  width: 800px;
  padding: 4px;

  .recordListName {
    font-size: 12px;
    color: #3d3d3d;
    margin: 16px 0 12px;
  }

  .recordListContent {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;

    .galleryItem {
      width: 192px;
      height: 296px;
      border-radius: 4px;
      transition: all 0.3s ease;
      background: #f8faff;

      .recordListContentItemImg {
        width: 100%;
        height: 228px;
        cursor: pointer;
        border-top-left-radius: 4px;
        border-top-right-radius: 4px;
        background-size: cover;
        // object-fit: cover;
        // display: block;
      }

      .recordListContentItemBot {
        display: flex;
        align-items: center;
        justify-content: space-between;
        height: 75px;
        padding: 3px 8px 0;
        background: #f8faff;
        border-bottom-left-radius: 4px;
        border-bottom-right-radius: 4px;

        .recordListContentItemBotTL {
          padding: 8px 0;

          .recordListContentItemBotTLT {
            font-size: 14px;
            font-weight: 500;
            color: #000;
            cursor: pointer;
          }

          .resultContent {
            font-size: 13px;
          }

          .recordListContentItemBotTLB {
            font-size: 12px;
            color: #999;
          }
        }

        .recordListContentItemBotTR {
          width: 22px;
          height: 22px;
          border-radius: 50%;
          background: #ebedf4;
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;
          visibility: hidden;
          position: absolute;
          right: 8px;

          .recordListContentItemDelete {
            width: 14px;
            height: 14px;
          }
        }
      }

      &:hover {
        transform: translateY(-2px) scale(1.01);
      }

      &:hover .recordListContentItemBotTR {
        visibility: visible;
      }
    }
  }

  .emptyMessageContainer {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    .emptyImage {
      width: 160px;
      height: 160px;
    }

    .emptyMessageText {
      font-size: 14px;
      color: #3d3d3d;
      margin-top: 12px;
    }
  }

  .recordListSpin {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 30px;

    :global(.semi-spin-wrapper) {
      color: #3498db !important;
    }

    span {
      margin-left: 12px;
    }
  }

  /* 结束消息 */
  .endMessage {
    padding: 20px;
    color: #999;
    font-size: 15px;
    text-align: center;
  }

  .endMessage p {
    margin-top: 30px;
  }
}

.loadTrigger {
  height: 1px;
  width: 100%;
  visibility: hidden;
}

.errorMessage {
  text-align: center;
  padding: 20px;
  color: #ff4d4f;
  font-size: 14px;
  background-color: #fff2f0;
  border: 1px solid #ffccc7;
  border-radius: 6px;
  margin: 10px 0;
}

.endMessage {
  text-align: center;
  padding: 20px;
  color: #999;
  font-size: 14px;
}

.videoCont {
  width: 100%;
  height: 221px;
  display: flex;
  align-items: center;
  background: #000;
  position: relative;
  cursor: pointer;
  border-radius: 4px 4px 0 0;

  .videoContW {
    position: absolute;
    top: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 50%);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 999;
    border-radius: 4px 4px 0 0;

    .videoContWI {
      width: 23px;
      height: 27px;
      cursor: pointer;
    }
  }
}

.noWeight {
  font-weight: normal !important;
}

.lazyImage {
  border-radius: 4px 4px 0 0 !important;

  :global(.galleryImage) {
    object-fit: contain;
  }
}

.textTips {
  max-height: 200px;
  overflow: auto;
  &::-webkit-scrollbar {
    display: none;
  }
}
