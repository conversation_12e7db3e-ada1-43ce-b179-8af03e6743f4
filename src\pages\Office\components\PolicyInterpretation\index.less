.policyInterpretation {
  width: 100%;
  overflow-y: auto;
  padding-bottom: 20px;
  .policyTab {
    width: 100%;
    height: 118px;
    display: flex;
    flex-wrap: wrap;
    flex-direction: row;
    gap: 12px;
    padding: 6px;
    .policyTabIn {
      width: 188px;
      height: 118px;
      border-radius: 8px;
      border: 1px solid #ebeef2;
      transition: all 0.3s ease;
      .gwyzc {
        width: 32px;
        height: 32px;
      }
      .gwyzcText {
        font-family: PingFang SC;
        font-size: 14px;
        font-weight: 500;
        color: #000;
        margin: 8px 0px 4px 0px;
      }
      .gwyzcDesc {
        font-family: PingFang SC;
        font-size: 12px;
        color: #999;
        white-space: nowrap;
      }
      &:hover {
        transform: translateY(-5px);
      }
    }
    :global(.semi-card-body) {
      padding: 16px;
    }
  }
}

.policyInterpretation::-webkit-scrollbar {
  display: none;
}