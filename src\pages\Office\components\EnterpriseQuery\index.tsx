import Example from '@/components/Example';
import { fetchEntCreditMenuInfo } from '@/services/enterpriseQuery';
import { calculateAndSetHeight } from '@/utils';
import { useEffect, useRef, useState } from 'react';
import styels from './index.less';

type Props = {
  sendMessage: (e: string) => void;
};
const EnterpriseQuery: React.FC<Props> = ({ sendMessage }) => {
  const enterpriseQueryRef = useRef<HTMLDivElement | null>(null);
  const [questionsList, setQuestionsList] = useState<{ question: string }[]>([]);

  useEffect(() => {
    const fetchData = async () => {
      try {
        const res = await fetchEntCreditMenuInfo();
        if (res.data) {
          setQuestionsList(
            res.data.config.questions.map((item) => {
              return { question: item };
            }),
          );
        }
      } catch (error) {
        console.error(error);
      }
    };
    fetchData();
  }, []);

  useEffect(() => {
    const element = document.querySelector('.semi-chat-inner');
    if (element) {
      const resizeObserver = new ResizeObserver((entries) => {
        entries.forEach(() => {
          calculateAndSetHeight(enterpriseQueryRef);
        });
      });
      resizeObserver.observe(element);
      return () => {
        resizeObserver.disconnect();
      };
    }
    return () => {};
  }, []);

  useEffect(() => {
    const handleResize = () => {
      calculateAndSetHeight(enterpriseQueryRef);
    };
    window.addEventListener('resize', handleResize);
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  return (
    <div ref={enterpriseQueryRef} className={styels.enterpriseQuery}>
      <Example
        description="您可以发起对企业的提问，智能助手帮您解答！"
        examplesTitle="提示示例"
        examplesDescription="您可以点击下列示例，智能助手帮您解答。"
        questions={questionsList}
        sendMessage={sendMessage}
      />
    </div>
  );
};

export default EnterpriseQuery;
