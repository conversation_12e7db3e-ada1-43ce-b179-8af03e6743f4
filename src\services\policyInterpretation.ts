import request from '@/utils/request';
import { Descendant } from 'slate';

// 获取政策解读子功能列表
export const getPolExpMenuListApi = '/ai/pol/getPolExpMenuList';

export type PolExpMenuListType = {
  appCode: null;
  desc: string;
  routeId: number;
  icon?: string;
  config: {
    questions: { question: string; webSite: string }[];
    textDesc: string;
    webSite: string;
    prompt: string;
  };
};

export const getPolExpMenuList = () =>
  request<PolExpMenuListType[]>(getPolExpMenuListApi, {
    method: 'GET',
  });
