.smartManagementChat {
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  margin: 0 auto;
  min-width: 800px;
  position: relative;

  .navButton {
    border-radius: 8px;
    height: 34px;
    padding: 6px 12px;
    transition: all 0.3s ease;
    font-size: 14px;
    line-height: 22px;
    font-weight: normal;
    color: #000;
    border: 1px solid #ebeef2;
    background-color: #fff;

    &:hover {
      transform: translateY(-2px);
      background-color: #fff;
      border: 1px solid #ebeef2;
    }
  }

  :global(.semi-button.developing) {
    cursor: pointer;
    background: #fff;
    box-sizing: border-box;
    border: 1px solid #ebeef2;
    font-size: 14px;
    color: #999;

    &:hover::after {
      content: '敬请期待';
      background: url('../../assets/chat/coming-soon.svg') no-repeat center center;
      position: absolute;
      top: -14px;
      right: 0;
      width: 56px;
      height: 20px;
      // background: #EEE;
      font-size: 12px;
      line-height: 18px;
      color: #999;
    }
  }

  :global(.semi-button.active) {
    background-color: #000;
    color: #fff;
  }
}

.smartManagementChatVision {
  justify-content: end;
  margin-bottom: 70px;
}

.smartMFilterTabs {
  position: absolute;
  top: 24px;
  width: 100%;

  .smartMFilterTabsButton {
    display: flex;
    gap: 12px;
  }
}

.reportValidTitle {
  font-family: 'PingFang SC';
  font-size: 24px;
  font-weight: 500;
  line-height: 32px;
  letter-spacing: normal;
  color: #000;
  text-align: center;
  margin-bottom: 12px;
}

.reportValidDesc {
  font-family: 'PingFang SC';
  font-size: 18px;
  font-weight: normal;
  line-height: 22px;
  letter-spacing: normal;
  color: #b1b0b0;
  text-align: center;
  margin-bottom: 24px;
}

.reportCompareTitle,
.enginTitle {
  font-family: 'PingFang SC';
  font-size: 24px;
  font-weight: 500;
  line-height: 32px;
  letter-spacing: normal;
  color: #000;
  text-align: center;
  margin-bottom: 32px;
}

.smartMFilterTabsTab {
  max-width: 800px;
  margin: 0 auto;
  padding-top: 24px;

  :global {
    .semi-tabs-content {
      padding: 0 !important;
    }
    .semi-tabs-bar {
      // position: sticky;
      // top: 0;
      z-index: 999;
      background: #fff;
    }
    .semi-tabs-tab {
      padding: 0 !important;
      height: 35px;
      font-family: Source Han Sans CN !important;
      font-size: 14px !important;
      font-weight: 350 !important;
      color: #000 !important;
      outline: none !important;
    }
    .semi-tabs-tab-single {
      margin-right: 32px !important;
    }
    .semi-tabs-tab:last-child {
      margin-right: 0 !important;
    }
    .semi-tabs-tab-active {
      font-weight: 500 !important;
      border-bottom-color: #000 !important;
    }
    .semi-tabs-bar-arrow {
      // display: none !important;
      position: relative;
      top: -8px;
    }
    .semi-tabs-pane {
      outline: none !important;
    }
  }
}

.visionTabContent {
  max-height: calc(100vh + 190px);
  height: 100%;
  overflow-y: auto;
  padding-bottom: 420px;
  .reportValidDescT {
    margin-top: 13px;
    font-size: 14px;
    padding: 0 13px;
    word-break: break-all;
  }
}

.visionTabContent::-webkit-scrollbar {
  width: 0;
  height: 0;
}
