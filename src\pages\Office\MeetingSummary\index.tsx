import { RefreshIcon } from '@/assets/office';
import { BackTopIcon, DownloadIcon, MeetingAvatarIcon } from '@/assets/svg';
import RightPane from '@/components/RightPane';
import type { AiSummaryType, DialogueRecordType } from '@/services/meeting';
import { fetchMeetDownLoad, fetchMeetTaskInfoById, meetSummary } from '@/services/meeting';
import { debounce, dispatchInUtils, downloadFile } from '@/utils';
import { Button, Spin, Tooltip, Typography } from '@douyinfe/semi-ui';
import React, { useEffect, useRef, useState } from 'react';
import { useLocation, useNavigate, useSearchParams, useSelector } from 'umi';
import AudioTranscriptPlayer from './components/AudioTranscriptPlayer';
import MeetingTitle from './components/MeetingTitle';
import styles from './index.less';

const MeetingSummary: React.FC = () => {
  const containerRef = useRef<HTMLDivElement | null>(null);
  const [searchParams] = useSearchParams();
  const { pathname } = useLocation();
  const location = useLocation();
  const navigate = useNavigate();
  const meetingId = searchParams.get('id') || '';

  // 对话记录
  const [dialogueRecord, setDialogueRecord] = useState<DialogueRecordType[]>([]);
  const [audioUrl, setAudioUrl] = useState('');
  const [duration, setDuration] = useState(0);
  const [aiSummary, setAiSummary] = useState<AiSummaryType>();
  // 对话列表加载loading
  const [recordLoading, setRecordLoading] = useState(true);
  const [loading, setLoading] = useState(false);
  const pageMode: string = useSelector(
    (state: { pageLayout: { mode: '' } }) => state.pageLayout.mode,
  );

  const chatContainerHide: boolean = useSelector(
    (state: { pageLayout: { chatContainerHide: false } }) => state.pageLayout.chatContainerHide,
  );

  const [meetingStateInfo, setMeetingStateInfo] = useState<{
    id: string;
    fileName: string;
  }>({ id: '', fileName: '' });

  useEffect(() => {
    if (pathname.includes('/meeting')) {
      setMeetingStateInfo(location.state as any);
    }
  }, [pathname]);

  // 离开页面的时候执行
  useEffect(() => {
    return () => {
      dispatchInUtils({
        type: 'pageLayout/changePageMode',
        payload: '',
      });
    };
  }, []);

  const getMeetingSummaryConfig = () => [
    {
      configKey: 'keyWords',
      name: '关键词',
      data: aiSummary?.keyWords || [],
      render: (config: any) => <KeywordsRender {...config} />,
    },
    {
      configKey: 'meetSummary',
      name: '会议概要',
      data: aiSummary?.meetSummary || '',
      render: (config: any) => <TextContentRender {...config} />,
    },
    {
      configKey: 'issues',
      name: '关键议题',
      data: aiSummary?.issues || '',
      render: (config: any) => <TextContentRender {...config} />,
    },
    {
      configKey: 'spokesSummary',
      name: '关键摘要',
      data: aiSummary?.spokesSummary || [],
      render: (config: any) => <KeySummaryRender {...config} />,
    },
  ];

  // 通过id获取会议详情
  const getMeetTaskInfoById = async (id: string) => {
    try {
      const res = await fetchMeetTaskInfoById({ id });
      if (res.data) {
        setDialogueRecord(res.data?.details || []);
        setAudioUrl(res.data?.audioUrl || '');
        setAiSummary(res.data?.aiSummary || {});
        setDuration(res.data?.duration || 0);
      }
    } catch (error) {
      console.log('获取会议详情失败');
    } finally {
      setRecordLoading(false);
    }
  };

  useEffect(() => {
    getMeetTaskInfoById(meetingId);
  }, [pageMode]);

  const handleDownload = debounce(async () => {
    try {
      const res = await fetchMeetDownLoad({ id: meetingId });
      if (res.data) {
        downloadFile(res.data.url, res.data.fileName);
      }
    } catch (error) {
      console.log('fetchMeetDownLoad', error);
    }
  }, 500);

  const handleRefresh = async () => {
    setLoading(true);
    try {
      const res = await meetSummary({ id: meetingId });
      if (res.data) {
        setAiSummary(res.data);
      }
    } catch (error) {
      console.log('meetSummary', error);
    } finally {
      setLoading(false);
    }
  };

  const getWrapStyle = function () {
    let s: Record<string, any> = {};
    if (['meeting'].indexOf(pageMode) > -1) {
      // s.paddingTop = 56;
      s.maxWidth = 'none';
    }
    if (chatContainerHide) s.display = 'none';
    return s;
  };

  // 关键词模块
  const KeywordsRender = (renderData: any) => {
    const { key, ...rest } = renderData; // 提取 key 并忽略它
    return (
      <div className={styles.keywordsRender}>
        <p className={styles.renderTitle}>{rest.name || ''}</p>
        <div className={styles.renderContent}>
          {rest.data && rest.data.length > 0
            ? rest.data.map((a: any, index: number) => (
                <div key={index} className={styles.keywordsRenderTag}>
                  {a}
                </div>
              ))
            : ''}
        </div>
      </div>
    );
  };

  // 会议概要、关键议题模块
  const TextContentRender = (renderData: any) => {
    const { key, ...rest } = renderData;
    return (
      <div className={styles.summaryRender}>
        <p className={styles.renderTitle}>{rest.name || ''}</p>
        <Typography.Text
          ellipsis={{
            rows: 4,
            expandable: true,
            collapsible: true,
            expandText: '展开全部',
            collapseText: '收起',
          }}
          style={{
            whiteSpace: 'pre-wrap',
            wordWrap: 'break-word',
            wordBreak: 'break-all',
            overflowWrap: 'break-word',
          }}
        >
          {rest.data || ''}
        </Typography.Text>
      </div>
    );
  };

  // 关键摘要模块
  const KeySummaryRender = (renderData: any) => {
    const { key, ...rest } = renderData;
    return (
      <div className={styles.keySummaryRender}>
        <p className={styles.renderTitle}>{rest.name || ''}</p>
        {rest.data && rest.data.length > 0
          ? rest.data.map((item: any, index: number) => (
              <div className={styles.transcriptItem} key={index}>
                <div className={styles.transcriptMeta}>
                  <MeetingAvatarIcon />
                  <span className={styles.transcriptSpeaker}>{item?.spkName || ''}</span>
                </div>
                <Typography.Text
                  className={styles.transcriptSummaryText}
                  ellipsis={{
                    rows: 5,
                    expandable: true,
                    collapsible: true,
                    expandText: '展开全部',
                    collapseText: '收起',
                  }}
                  style={{
                    whiteSpace: 'pre-wrap',
                    wordWrap: 'break-word',
                    wordBreak: 'break-all',
                    overflowWrap: 'break-word',
                  }}
                >
                  {item?.spkSummary || ''}
                </Typography.Text>
              </div>
            ))
          : ''}
      </div>
    );
  };

  const scrollToTop = () => {
    const container = containerRef.current;
    if (container) container.scrollTo({ top: 0, behavior: 'smooth' });
  };

  const scrollToBottom = () => {
    const container = containerRef.current;
    if (container) container.scrollTo({ top: container.scrollHeight, behavior: 'smooth' });
  };

  const RightContainerRender = () => {
    const config = getMeetingSummaryConfig();
    return (
      pageMode === 'meeting' && (
        <RightPane
          contentRender={() => (
            <div
              className={`${styles.meetingSummaryRightPane} ${styles.scrollBarCustom}`}
              ref={containerRef}
            >
              <p className={styles.title}>智能总结</p>
              {config.map((item, index) => {
                return (
                  <div className={styles.container} key={item.configKey}>
                    {item.render(item)}
                  </div>
                );
              })}
              {/* 滚动控制按钮 */}
              <div className={styles.scrollControls}>
                <BackTopIcon className={styles.scrollIcon} onClick={scrollToTop} />
                <BackTopIcon
                  className={[`${styles.scrollIcon} ${styles.scrollIconBottom}`]}
                  onClick={scrollToBottom}
                />
              </div>
            </div>
          )}
          leftActionRender={() => (
            <Tooltip content={'刷新'}>
              <Button theme="borderless" icon={<RefreshIcon />} onClick={handleRefresh}></Button>
            </Tooltip>
          )}
          rightActionRender={() => (
            <Tooltip content={'下载'}>
              <Button theme="borderless" icon={<DownloadIcon />} onClick={handleDownload}></Button>
            </Tooltip>
          )}
        />
      )
    );
  };

  const meetingHeaderClick = () => {
    setTimeout(() => {
      dispatchInUtils({
        type: 'pageLayout/changePageMode',
        payload: '',
      });
    }, 0);
    navigate(-1);
  };

  return (
    <div className={styles.meetingSummary}>
      <div className={styles.meetingSummaryContainer} style={getWrapStyle()}>
        {/* 全局关键字搜索 */}
        {['meeting'].indexOf(pageMode) > -1 && (
          <div className={styles.meetingSummaryHeader}>
            <MeetingTitle title={meetingStateInfo?.fileName} onBackClick={meetingHeaderClick} />
            {/* <GlobalSearch
              className={styles.meetingSummaryGlobalSearch}
              contentArea={`.${styles.meetingSummaryContainer}`}
            /> */}
          </div>
        )}
        {/* 音频字幕播放 */}
        {recordLoading ? (
          <div className={styles.spinLoading}>
            <Spin tip="加载中..." />
          </div>
        ) : (
          <AudioTranscriptPlayer
            audioSrc={audioUrl}
            transcript={dialogueRecord}
            duration={duration}
            fetchDetailInfo={getMeetTaskInfoById}
          />
        )}
      </div>
      {/* 右屏智能总结展示 */}
      {RightContainerRender()}
      {loading && (
        <div className={styles.spinLoading}>
          <Spin size="large" />
        </div>
      )}
    </div>
  );
};

export default MeetingSummary;
