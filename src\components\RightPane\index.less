.rightPane {
  flex: 1;
  margin-left: 1px;
  box-sizing: border-box;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  background: #F6F6F6;
  box-shadow: 0 4px 10px 4px rgba(158, 158, 158, 20%);

  .rightPaneToolsBar {
    height: 53px;
    border-bottom: 1px solid #eee;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    box-sizing: border-box;
    padding: 16px 24px;
    position: relative;
    z-index: 100;
    background: #fff;

    &.isRoute {
      padding: 0 20px;
    }

    .btnThumbControl {
      padding-right: 10px;
      box-sizing: border-box;
      cursor: pointer;

      img {
        width: 24px;
        height: auto;
      }
    }

    .title {
      flex: 1;
      padding-left: 10px;
      margin-top: 3px;
    }
  }
}
