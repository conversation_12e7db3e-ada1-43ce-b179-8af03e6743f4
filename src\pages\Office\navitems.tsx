import { FetchHistoryPPTParams } from '@/models/historyChat';
import { getHistoryPPTs, getOfficeTools } from './office';

//   const developingApp = ['pdf', 'image', 'video', 'speech', 'tts', 'coding'];
/**
 * 高效办公 - 导航定义
 * disabled: true 表示该导航项不可用
 * active: true 表示当前选中导航项
 */
export const navItems = [
  { key: 'write', text: '帮我写作', active: true, disabled: false },
  { key: 'reading', text: 'AI阅读', disabled: false },
  { key: 'ppt', text: '智能PPT', disabled: false },
  { key: 'image', text: '文生图', disabled: false },
  { key: 'pol_exp', text: '政策解读', disabled: false },
  { key: 'report_gen', text: '报告生成', disabled: false },
  { key: 'meeting', text: '会议纪要生成', disabled: false },
  { key: 'ent_credit', text: '企业查询', disabled: false },
  // { key: 'pdf', text: 'PDF编辑', disabled: true },
  // { key: 'video', text: '文生视频', disabled: true },
  // { key: 'speech', text: '语音转写', disabled: true },
  // { key: 'tts', text: '语音合成', disabled: true },
  // { key: 'coding', text: 'AI编程', disabled: true },
];

const defaultIcon = <div />;

// 封装获取工具列表的方法
export const fetchOfficeTools = async () => {
  try {
    const res = await getOfficeTools({ pageNum: 0, pageSize: 1000 });
    // 检查不同的数据格式并适当处理
    let toolsData: any[] = [];

    if (res && res.data) {
      if (Array.isArray(res.data)) {
        toolsData = res.data;
      }
    }

    if (toolsData && toolsData.length > 0) {
      // 处理后端返回的数据，添加图标
      const processedTools = toolsData.map((tool: any) => {
        // 提取字段数据
        const id = tool.id || '';
        const promptText = tool.prompt || '';
        const writeTitle = tool.title || '';
        const displayDesc = tool.text || '';
        const icon = tool.url || '';

        // 使用后端返回的URL作为图标
        let iconElement;
        try {
          if (icon) {
            iconElement = <img src={icon} alt={writeTitle} className="tool-icon" />;
          } else {
            iconElement = defaultIcon;
          }
        } catch (e) {
          iconElement = defaultIcon;
        }

        return {
          id,
          title: writeTitle,
          desc: displayDesc,
          prompt: promptText,
          icon: iconElement,
        };
      });

      return processedTools;
    }
    return [];
  } catch (error) {
    return [];
  }
};

// 导出空的初始工具列表
export const officeTools: any[] = [];

export const fetchHistoryPPTData = async (params: FetchHistoryPPTParams) => {
  try {
    const response = await getHistoryPPTs(params);
    return response;
  } catch (error) {
    console.error('获取历史PPT数据失败:', error);
    return {
      code: 500,
      data: { list: [], total: 0 },
      msg: '获取历史PPT数据失败',
      hostName: '',
    };
  }
};
