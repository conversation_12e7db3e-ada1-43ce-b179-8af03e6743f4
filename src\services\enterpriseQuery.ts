import request from '@/utils/request';

// 获取企业查询子功能列表
export const getEntCreditMenuInfoApi = '/ai/ent/getEntCreditMenuInfo';

export type configType = {
  routeId: number;
  desc: string;
  appCode: string;
  config: {
    intentions: {
      desc: string;
      url: string;
      prompt: string;
      routeId: number;
      requestType: string;
    }[];
    questions: string[];
  };
};

/**
 * 获取企业查询子功能列表
 *
 */
export const fetchEntCreditMenuInfo = () =>
  request<configType>(getEntCreditMenuInfoApi, {
    method: 'GET',
  });
