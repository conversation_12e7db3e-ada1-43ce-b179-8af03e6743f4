import request from '@/utils/request';

// 根据RouteId获取视觉工程识别列表API
export const getVisualEngineeringListApi = '/ai/vision/getHistory';
// 根据Id删除视觉工程数据
export const deleteVisualEngineeringDataApi = '/ai/vision/deleteById';
// 视觉工程识别
export const visualEngineeringApi = '/ai/vision/ident';

export interface VisualEngineeringListItemType {
  id: number;
  routeId: number;
  modelType: string;
  uploadUrl: string;
  identUrl: string;
  identCount: number | null;
  createTime: Date;
  resultList: string[] | null;
}

/**
 * 根据RouteId获取视觉工程识别列表API
 * @param { routeId: number } 路由ID
 * @returns
 */
export const getVisualEngineeringList = (params: { routeId: number }) =>
  request<VisualEngineeringListItemType[]>(getVisualEngineeringListApi, {
    method: 'GET',
    params,
  });

/**
 * 根据Id删除视觉工程数据
 * @param { id: number } 数据ID
 * @returns
 * */
export const deleteVisualEngineeringData = (params: { id: number }) =>
  request<any>(deleteVisualEngineeringDataApi, {
    method: 'GET',
    params,
  });

/**
 * 视觉工程识别
 * @param { appCode: string, routeId: number, uploadUrls: string[] } 应用Code, 路由ID,上传的图片地址文件
 * @returns
 * */
export const visualEngineering = (params: {
  appCode: string;
  routeId: number;
  uploadUrls: string[];
}) =>
  request<VisualEngineeringListItemType[]>(visualEngineeringApi, {
    method: 'POST',
    data: params,
  });
