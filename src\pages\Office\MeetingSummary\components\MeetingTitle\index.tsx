import { ArrowLeftIcon } from '@/assets/svg';
import { Typography } from '@douyinfe/semi-ui';
import React from 'react';
import styles from './index.less';

interface MeetingTitleProps {
  title?: string;
  className?: string;
  onBackClick: () => void;
}

const MeetingTitle: React.FC<MeetingTitleProps> = ({ className, title, onBackClick }) => {
  return (
    <div className={`${styles.meetingHeader} ${className}`}>
      <ArrowLeftIcon className={styles.meetingHeader_icon} onClick={onBackClick} />
      <Typography.Title ellipsis={{ showTooltip: true }} className={styles.meetingHeader_title}>
        {title || ''}
      </Typography.Title>
    </div>
  );
};

export default MeetingTitle;
