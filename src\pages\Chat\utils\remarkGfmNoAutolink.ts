import remarkGfm from 'remark-gfm';

/**
 * 自定义 remark-gfm 插件，禁用自动链接功能
 * 保留其他 GFM 功能（表格、删除线、任务列表等）
 */
export function remarkGfmNoAutolink() {
  // 获取 remark-gfm 的配置
  const gfmPlugin = remarkGfm({
    // 禁用自动链接
    singleTilde: false, // 禁用单个 ~ 删除线（保留双 ~）
  });

  return (tree: any, file: any) => {
    // 调用原始的 remark-gfm 插件
    if (typeof gfmPlugin === 'function') {
      const result = gfmPlugin(tree, file);
      return result;
    }
    return tree;
  };
}
