/**
 * 视频文件格式
 */
export const VIDEO_TYPE = '.mp4, .flv, .mov, .avi';

/**
 * 音频文件格式
 */
export const AUDIO_TYPE = '.mp3, .aac, .wav, .m4a';

/**
 * 上传文件格式placeholder
 */
export const UPLOAD_PLACEHOLDER = '支持 MP4，MP3，FLV，AAC，MOV，WAV，AVI，M4A文件大小不超过500M';

/**
 * 音频转写状态
 */
export enum STATUS_TYPE {
  ERROR = -1, // 转写失败
  QUEUED = 0, // 排队中
  GENERATING = 1, // 转写中
  COMPLETED = 2, // 转写成功
  CANCELED = 3, // 任务取消
}

export const STATUS_TEXT: Record<STATUS_TYPE, string> = {
  [STATUS_TYPE.ERROR]: '转写失败',
  [STATUS_TYPE.QUEUED]: '排队中',
  [STATUS_TYPE.GENERATING]: '转写中',
  [STATUS_TYPE.COMPLETED]: '转写成功',
  [STATUS_TYPE.CANCELED]: '任务取消',
};
