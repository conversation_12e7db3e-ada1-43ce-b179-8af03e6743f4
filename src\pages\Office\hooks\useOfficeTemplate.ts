import { AuthModelState } from '@/models/auth';
import { PPTItem } from '@/models/historyChat';
import { convertTextToRichInput } from '@/utils';
import { useEffect, useState } from 'react';
import { useDispatch, useLocation, useParams, useSearchParams, useSelector } from 'umi';
import { fetchHistoryPPTData, fetchOfficeTools, navItems } from '../navitems';

interface UseOfficeTemplateProps {
  currNav?: string;
  changeNav?: (nav: string) => void;
  onSelectTemplate?: (template: any) => void;
}

export const useOfficeTemplate = ({
  currNav,
  changeNav,
  onSelectTemplate,
}: UseOfficeTemplateProps = {}) => {
  const getUrlActiveTab = () => {
    const urlParams = new URLSearchParams(window.location.search);
    return urlParams.get('activeTab') || currNav || 'write';
  };
  const [activeNav, setActiveNav] = useState(getUrlActiveTab());
  const [tools, setTools] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedCardIndex, setSelectedCardIndex] = useState<number | null>(null);
  const [pptList, setPptList] = useState<PPTItem[]>([]);
  const [pptLoading, setPptLoading] = useState(true);
  const [pptListTotal, setPptListTotal] = useState<number>(0);
  const { chatId } = useParams<{ chatId: string }>();
  const dispatch = useDispatch();
  const location = useLocation();
  const [searchParams, setSearchParams] = useSearchParams();
  const { user } = useSelector((state: { auth: AuthModelState }) => state.auth);

  // 清除富文本状态的通用函数
  const clearRichTextState = () => {
    setSelectedCardIndex(null);
    // 使用安全的默认值清除富文本状态
    dispatch({
      type: 'chat/setRichTextContent',
      payload: {
        useRichText: false,
        richTextValue: [
          {
            type: 'paragraph',
            children: [{ text: '' }],
          },
        ],
      },
    });
  };

  // 获取工具列表
  const getToolsList = async (navKey = 'write') => {
    setLoading(true);
    try {
      const navItem = navItems.find((item) => item.key === navKey);
      if (navItem) {
        if (navKey === 'write') {
          const toolsList = await fetchOfficeTools();
          setTools(toolsList);
        }
      }
    } catch (error) {
      console.error('获取工具列表失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 获取历史PPT数据
  const getHistoryPPTData = async (userId: number | undefined, pageSize = 9, pageNum = 1) => {
    // if (navKey !== 'ppt') {
    //   return;
    // }
    if (!userId) {
      console.error('userId 不能为空');
      // Toast.error('用户Id 不能为空');
      return;
    }
    setPptLoading(true);
    const { data }: { data: any } = await fetchHistoryPPTData({ userId, pageSize, pageNum });
    setPptList(data?.list || []);
    setPptListTotal(data?.total || 0);
    setPptLoading(false);
  };

  // 处理导航点击
  const handleNavClick = (key: string) => {
    if (activeNav === key) return;
    clearRichTextState();
    setActiveNav(key);
    changeNav?.(key);
    if (chatId) {
      const newParams = new URLSearchParams(searchParams);
      newParams.set('appCode', key);
      setSearchParams(newParams, { state: location.state });
    }
    getToolsList(key);
  };

  // 处理模板选择
  const handleTemplateSelect = (index: number) => {
    setSelectedCardIndex(index);

    const toolItem = tools[index];
    if (toolItem) {
      const toolPrompt = toolItem.prompt || toolItem.title || toolItem.desc || '';

      if (toolPrompt.includes('[')) {
        const richTextValue = convertTextToRichInput(toolPrompt);
        dispatch({
          type: 'chat/setRichTextContent',
          payload: {
            useRichText: true,
            richTextValue: richTextValue,
          },
        });
      }

      if (onSelectTemplate) {
        onSelectTemplate({
          ...toolItem,
          message: toolPrompt,
        });
      }
    }
  };

  // 初始化加载
  useEffect(() => {
    getToolsList(activeNav);
    if (activeNav === 'ppt') {
      getHistoryPPTData(user?.id);
    }
  }, [activeNav]);

  return {
    activeNav,
    tools,
    loading,
    selectedCardIndex,
    handleNavClick,
    handleTemplateSelect,
    clearRichTextState,
    pptList,
    pptListTotal,
    pptLoading,
    getHistoryPPTData,
  };
};
