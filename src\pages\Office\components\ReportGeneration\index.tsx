import Example from '@/components/Example';
import { HistoryModelState } from '@/models/historyChat';
import { calculateAndSetHeight, dispatchInUtils } from '@/utils';
import { useEffect, useRef } from 'react';
import { useSelector } from 'umi';
import styels from './index.less';

type Props = {
  sendMessage: (e: string, routeId?: number) => void;
};
const ReportGeneration: React.FC<Props> = ({ sendMessage }) => {
  const { questionsList } = useSelector(
    (state: { historyChat: HistoryModelState }) => state.historyChat,
  );

  const reportGenerationRef = useRef<HTMLDivElement | null>(null);

  useEffect(() => {
    const element = document.querySelector('.semi-chat-inner');
    if (element) {
      const resizeObserver = new ResizeObserver((entries) => {
        entries.forEach(() => {
          calculateAndSetHeight(reportGenerationRef);
        });
      });
      resizeObserver.observe(element);
      return () => {
        resizeObserver.disconnect();
      };
    }
    return () => {};
  }, []);

  useEffect(() => {
    const handleResize = () => {
      calculateAndSetHeight(reportGenerationRef);
    };
    window.addEventListener('resize', handleResize);
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  useEffect(() => {
    dispatchInUtils({
      type: 'chat/setRichTextContent',
      payload: {
        useRichText: true,
        // richTextValue: [
        //   {
        //     type: 'paragraph',
        //     children: [{ text: '' }],
        //   },
        // ],
        richTextValue: '',
      },
    });
  }, []);

  return (
    <div ref={reportGenerationRef} className={styels.reportGeneration}>
      <div className={styels.reportGeneration_title}>一站式AI报告生成</div>
      <Example
        description="您可以发起对报告的创作要求，智能助手帮您生成报告！"
        examplesTitle="提示示例"
        examplesDescription="您可以点击下列示例，智能助手快速生成报告内容"
        questions={questionsList}
        sendMessage={sendMessage}
      />
    </div>
  );
};

export default ReportGeneration;
